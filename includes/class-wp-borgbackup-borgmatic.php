<?php
/**
 * Borgmatic configuration management for WP BorgBackup
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_BorgBackup_Borgmatic {
    
    /**
     * Configuration directory
     */
    private $config_dir;
    
    /**
     * SSH keys directory
     */
    private $ssh_dir;
    
    /**
     * Constructor
     */
    public function __construct() {
        $upload_dir = wp_upload_dir();
        $this->config_dir = $upload_dir['basedir'] . '/wp-borgbackup/configs';
        $this->ssh_dir = $upload_dir['basedir'] . '/wp-borgbackup/ssh';
        
        $this->ensure_directories();
    }
    
    /**
     * Ensure required directories exist
     */
    private function ensure_directories() {
        if (!file_exists($this->config_dir)) {
            wp_mkdir_p($this->config_dir);
            // Protect directory with .htaccess
            file_put_contents($this->config_dir . '/.htaccess', "Deny from all\n");
        }
        
        if (!file_exists($this->ssh_dir)) {
            wp_mkdir_p($this->ssh_dir);
            // Protect directory with .htaccess and set proper permissions
            file_put_contents($this->ssh_dir . '/.htaccess', "Deny from all\n");
            chmod($this->ssh_dir, 0700);
        }
    }
    
    /**
     * Generate Borgmatic configuration
     */
    public function generate_config($config_data) {
        $config = array();
        
        // Source directories
        $config['source_directories'] = $this->get_source_directories($config_data);
        
        // Repository configuration
        $config['repositories'] = array($config_data['repository_url']);
        
        // SSH configuration
        if (!empty($config_data['ssh_key_path'])) {
            $config['ssh_command'] = 'ssh -i ' . $config_data['ssh_key_path'];
        }
        
        // Encryption
        if (!empty($config_data['encryption_passphrase'])) {
            $config['encryption_passphrase'] = $config_data['encryption_passphrase'];
        }
        
        // Archive naming
        $config['archive_name_format'] = $this->get_archive_name_format($config_data);
        
        // Exclude patterns
        $config['exclude_patterns'] = $this->get_exclude_patterns($config_data);
        
        // Retention policy
        $config = array_merge($config, $this->get_retention_policy($config_data));
        
        // Consistency checks
        $config['checks'] = $this->get_consistency_checks($config_data);
        
        // Hooks for database backup
        $config = array_merge($config, $this->get_hooks($config_data));
        
        return $config;
    }
    
    /**
     * Get source directories to backup
     */
    private function get_source_directories($config_data) {
        $directories = array();
        
        // WordPress root (excluding some directories that will be handled separately)
        $wp_root = ABSPATH;
        $directories[] = $wp_root;
        
        // Add specific directories if configured
        if (!empty($config_data['source_directories'])) {
            if (is_array($config_data['source_directories'])) {
                $directories = array_merge($directories, $config_data['source_directories']);
            }
        }
        
        return array_unique($directories);
    }
    
    /**
     * Get exclude patterns
     */
    private function get_exclude_patterns($config_data) {
        $patterns = array(
            // WordPress core files that can be re-downloaded
            'wp-admin',
            'wp-includes',
            
            // Cache directories
            'wp-content/cache',
            'wp-content/*/cache',
            'wp-content/w3tc-config',
            'wp-content/wp-rocket-config',
            
            // Temporary files
            '*.tmp',
            '*.log',
            '*.pid',
            
            // System files
            '.DS_Store',
            'Thumbs.db',
            
            // Version control
            '.git',
            '.svn',
            '.hg',
            
            // Node modules and build files
            'node_modules',
            'bower_components',
            '*.min.js',
            '*.min.css',
            
            // Large media files (optional)
            '*.mp4',
            '*.avi',
            '*.mov',
            '*.wmv',
            '*.flv',
            '*.mkv',
        );
        
        // Add custom exclude patterns
        if (!empty($config_data['exclude_patterns'])) {
            if (is_array($config_data['exclude_patterns'])) {
                $patterns = array_merge($patterns, $config_data['exclude_patterns']);
            }
        }
        
        return $patterns;
    }
    
    /**
     * Get archive name format
     */
    private function get_archive_name_format($config_data) {
        $site_name = sanitize_title(get_bloginfo('name'));
        return $site_name . '-{now:%Y-%m-%d-%H%M%S}';
    }
    
    /**
     * Get retention policy
     */
    private function get_retention_policy($config_data) {
        $retention = array();
        
        if (!empty($config_data['retention_policy'])) {
            $policy = $config_data['retention_policy'];
            
            if (isset($policy['keep_daily'])) {
                $retention['keep_daily'] = (int) $policy['keep_daily'];
            }
            if (isset($policy['keep_weekly'])) {
                $retention['keep_weekly'] = (int) $policy['keep_weekly'];
            }
            if (isset($policy['keep_monthly'])) {
                $retention['keep_monthly'] = (int) $policy['keep_monthly'];
            }
            if (isset($policy['keep_yearly'])) {
                $retention['keep_yearly'] = (int) $policy['keep_yearly'];
            }
        } else {
            // Default retention policy
            $retention = array(
                'keep_daily' => 7,
                'keep_weekly' => 4,
                'keep_monthly' => 6,
                'keep_yearly' => 1,
            );
        }
        
        return $retention;
    }
    
    /**
     * Get consistency checks configuration
     */
    private function get_consistency_checks($config_data) {
        return array(
            array(
                'name' => 'repository',
                'frequency' => '2 weeks'
            ),
            array(
                'name' => 'archives',
                'frequency' => '4 weeks'
            )
        );
    }
    
    /**
     * Get hooks for database backup
     */
    private function get_hooks($config_data) {
        $hooks = array();
        
        // Only add database hooks if database backup is enabled
        if (!empty($config_data['include_database'])) {
            $db_backup_path = $this->get_database_backup_path();
            
            $hooks['before_backup'] = array(
                $this->get_database_backup_command($db_backup_path)
            );
            
            $hooks['after_backup'] = array(
                'rm -f ' . $db_backup_path
            );
        }
        
        return $hooks;
    }
    
    /**
     * Get database backup command
     */
    private function get_database_backup_command($backup_path) {
        $db_host = DB_HOST;
        $db_name = DB_NAME;
        $db_user = DB_USER;
        $db_password = DB_PASSWORD;
        
        // Handle port in DB_HOST
        $port_option = '';
        if (strpos($db_host, ':') !== false) {
            list($db_host, $port) = explode(':', $db_host, 2);
            $port_option = " --port={$port}";
        }
        
        return sprintf(
            'mysqldump --host=%s%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
            escapeshellarg($db_host),
            $port_option,
            escapeshellarg($db_user),
            escapeshellarg($db_password),
            escapeshellarg($db_name),
            escapeshellarg($backup_path)
        );
    }
    
    /**
     * Get database backup file path
     */
    private function get_database_backup_path() {
        $upload_dir = wp_upload_dir();
        $backup_dir = $upload_dir['basedir'] . '/wp-borgbackup/temp';
        
        if (!file_exists($backup_dir)) {
            wp_mkdir_p($backup_dir);
        }
        
        return $backup_dir . '/database-' . date('Y-m-d-H-i-s') . '.sql';
    }
    
    /**
     * Save configuration to file
     */
    public function save_config($config_id, $config_data) {
        $config = $this->generate_config($config_data);
        $config_file = $this->config_dir . '/config-' . $config_id . '.yaml';
        
        // Convert to YAML format
        $yaml_content = $this->array_to_yaml($config);
        
        $result = file_put_contents($config_file, $yaml_content);
        
        if ($result === false) {
            return new WP_Error('file_write_error', __('Failed to write configuration file', 'wp-borgbackup'));
        }
        
        // Set proper permissions
        chmod($config_file, 0600);
        
        return $config_file;
    }
    
    /**
     * Get configuration file path
     */
    public function get_config_file_path($config_id) {
        return $this->config_dir . '/config-' . $config_id . '.yaml';
    }
    
    /**
     * Delete configuration file
     */
    public function delete_config($config_id) {
        $config_file = $this->get_config_file_path($config_id);
        
        if (file_exists($config_file)) {
            return unlink($config_file);
        }
        
        return true;
    }
    
    /**
     * Save SSH private key
     */
    public function save_ssh_key($config_id, $private_key) {
        $key_file = $this->ssh_dir . '/key-' . $config_id;
        
        $result = file_put_contents($key_file, $private_key);
        
        if ($result === false) {
            return new WP_Error('file_write_error', __('Failed to write SSH key file', 'wp-borgbackup'));
        }
        
        // Set proper permissions for SSH key
        chmod($key_file, 0600);
        
        return $key_file;
    }
    
    /**
     * Get SSH key file path
     */
    public function get_ssh_key_path($config_id) {
        return $this->ssh_dir . '/key-' . $config_id;
    }
    
    /**
     * Delete SSH key file
     */
    public function delete_ssh_key($config_id) {
        $key_file = $this->get_ssh_key_path($config_id);
        
        if (file_exists($key_file)) {
            return unlink($key_file);
        }
        
        return true;
    }
    
    /**
     * Convert array to YAML format
     */
    private function array_to_yaml($array, $indent = 0) {
        $yaml = '';
        $spaces = str_repeat('    ', $indent);
        
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                if (array_keys($value) === range(0, count($value) - 1)) {
                    // Indexed array (list)
                    $yaml .= $spaces . $key . ":\n";
                    foreach ($value as $item) {
                        if (is_array($item)) {
                            $yaml .= $spaces . "    -\n";
                            $yaml .= $this->array_to_yaml($item, $indent + 2);
                        } else {
                            $yaml .= $spaces . '    - ' . $this->yaml_escape($item) . "\n";
                        }
                    }
                } else {
                    // Associative array
                    $yaml .= $spaces . $key . ":\n";
                    $yaml .= $this->array_to_yaml($value, $indent + 1);
                }
            } else {
                $yaml .= $spaces . $key . ': ' . $this->yaml_escape($value) . "\n";
            }
        }
        
        return $yaml;
    }
    
    /**
     * Escape YAML values
     */
    private function yaml_escape($value) {
        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }
        
        if (is_numeric($value)) {
            return $value;
        }
        
        if (is_string($value)) {
            // Quote strings that contain special characters
            if (preg_match('/[:\[\]{}|>*&!%@`]/', $value) || 
                preg_match('/^\s|\s$/', $value) ||
                is_numeric($value)) {
                return '"' . str_replace('"', '\"', $value) . '"';
            }
        }
        
        return $value;
    }
    
    /**
     * Validate Borgmatic installation
     */
    public function validate_borgmatic_installation() {
        $output = array();
        $return_code = 0;
        
        exec('which borgmatic 2>/dev/null', $output, $return_code);
        
        if ($return_code !== 0 || empty($output)) {
            return new WP_Error(
                'borgmatic_not_found',
                __('Borgmatic is not installed or not in PATH. Please install Borgmatic first.', 'wp-borgbackup')
            );
        }
        
        // Check version
        $output = array();
        exec('borgmatic --version 2>/dev/null', $output, $return_code);
        
        if ($return_code === 0 && !empty($output)) {
            return array(
                'installed' => true,
                'path' => trim($output[0]),
                'version' => isset($output[0]) ? trim($output[0]) : 'unknown'
            );
        }
        
        return array(
            'installed' => true,
            'path' => '/usr/local/bin/borgmatic',
            'version' => 'unknown'
        );
    }
    
    /**
     * Get Borgmatic command
     */
    public function get_borgmatic_command($config_file, $action = 'create') {
        $borgmatic_path = 'borgmatic';
        
        // Try to find borgmatic in common locations
        $common_paths = array(
            '/usr/local/bin/borgmatic',
            '/usr/bin/borgmatic',
            '/opt/homebrew/bin/borgmatic'
        );
        
        foreach ($common_paths as $path) {
            if (file_exists($path)) {
                $borgmatic_path = $path;
                break;
            }
        }
        
        $command = sprintf(
            '%s --config %s %s',
            escapeshellarg($borgmatic_path),
            escapeshellarg($config_file),
            escapeshellarg($action)
        );
        
        return $command;
    }
}
