<?php
/**
 * Admin interface for WP BorgBackup
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_BorgBackup_Admin {
    
    /**
     * Database instance
     */
    private $database;
    
    /**
     * BorgBase API instance
     */
    private $borgbase_api;
    
    /**
     * Scheduler instance
     */
    private $scheduler;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new WP_BorgBackup_Database();
        $this->borgbase_api = new WP_BorgBackup_BorgBase_API();
        $this->scheduler = new WP_BorgBackup_Scheduler();
        
        add_action('admin_init', array($this, 'handle_form_submissions'));
    }
    
    /**
     * Display main page
     */
    public function display_main_page() {
        $configs = $this->database->get_backup_configs();
        $recent_backups = $this->database->get_backup_history(null, 10);
        $schedule_status = $this->scheduler->get_schedule_status();
        
        include WP_BORGBACKUP_PLUGIN_DIR . 'admin/views/main-page.php';
    }
    
    /**
     * Display settings page
     */
    public function display_settings_page() {
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'general';
        
        switch ($active_tab) {
            case 'borgbase':
                $this->display_borgbase_settings();
                break;
            case 'backup':
                $this->display_backup_settings();
                break;
            case 'retention':
                $this->display_retention_settings();
                break;
            case 'schedule':
                $this->display_schedule_settings();
                break;
            case 'notifications':
                $this->display_notification_settings();
                break;
            default:
                $this->display_general_settings();
                break;
        }
    }
    
    /**
     * Display general settings
     */
    private function display_general_settings() {
        $settings = get_option('wp_borgbackup_settings', array());
        include WP_BORGBACKUP_PLUGIN_DIR . 'admin/views/settings-general.php';
    }
    
    /**
     * Display BorgBase settings
     */
    private function display_borgbase_settings() {
        $settings = get_option('wp_borgbackup_borgbase_settings', array());
        $repositories = array();
        
        if (!empty($settings['api_key'])) {
            $this->borgbase_api->set_api_key($settings['api_key']);
            $repos_result = $this->borgbase_api->get_repositories();
            if (!is_wp_error($repos_result)) {
                $repositories = $repos_result;
            }
        }
        
        include WP_BORGBACKUP_PLUGIN_DIR . 'admin/views/settings-borgbase.php';
    }
    
    /**
     * Display backup settings
     */
    private function display_backup_settings() {
        $settings = get_option('wp_borgbackup_backup_settings', array());
        include WP_BORGBACKUP_PLUGIN_DIR . 'admin/views/settings-backup.php';
    }
    
    /**
     * Display retention settings
     */
    private function display_retention_settings() {
        $settings = get_option('wp_borgbackup_retention_settings', array());
        include WP_BORGBACKUP_PLUGIN_DIR . 'admin/views/settings-retention.php';
    }
    
    /**
     * Display schedule settings
     */
    private function display_schedule_settings() {
        $settings = get_option('wp_borgbackup_schedule_settings', array());
        $frequencies = $this->scheduler->get_available_frequencies();
        $schedule_status = $this->scheduler->get_schedule_status();
        include WP_BORGBACKUP_PLUGIN_DIR . 'admin/views/settings-schedule.php';
    }
    
    /**
     * Display notification settings
     */
    private function display_notification_settings() {
        $settings = get_option('wp_borgbackup_notification_settings', array());
        include WP_BORGBACKUP_PLUGIN_DIR . 'admin/views/settings-notifications.php';
    }
    
    /**
     * Display backup history page
     */
    public function display_history_page() {
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 20;
        $offset = ($page - 1) * $per_page;
        
        $config_filter = isset($_GET['config_id']) ? intval($_GET['config_id']) : null;
        
        $backups = $this->database->get_backup_history($config_filter, $per_page, $offset);
        $configs = $this->database->get_backup_configs();
        
        // Get total count for pagination
        global $wpdb;
        $total_query = "SELECT COUNT(*) FROM {$wpdb->prefix}borgbackup_history";
        if ($config_filter) {
            $total_query .= $wpdb->prepare(" WHERE config_id = %d", $config_filter);
        }
        $total_items = $wpdb->get_var($total_query);
        
        include WP_BORGBACKUP_PLUGIN_DIR . 'admin/views/history-page.php';
    }
    
    /**
     * Display logs page
     */
    public function display_logs_page() {
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 50;
        $offset = ($page - 1) * $per_page;
        
        $backup_filter = isset($_GET['backup_id']) ? intval($_GET['backup_id']) : null;
        $level_filter = isset($_GET['level']) ? sanitize_text_field($_GET['level']) : null;
        
        $logger = new WP_BorgBackup_Logger();
        $logs = $logger->get_logs($backup_filter, $level_filter, $per_page, $offset);
        
        // Get recent backups for filter dropdown
        $recent_backups = $this->database->get_backup_history(null, 50);
        
        include WP_BORGBACKUP_PLUGIN_DIR . 'admin/views/logs-page.php';
    }
    
    /**
     * Handle form submissions
     */
    public function handle_form_submissions() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        // Handle settings updates
        if (isset($_POST['wp_borgbackup_update_settings'])) {
            $this->handle_settings_update();
        }
        
        // Handle backup configuration creation/update
        if (isset($_POST['wp_borgbackup_save_config'])) {
            $this->handle_config_save();
        }
        
        // Handle backup configuration deletion
        if (isset($_POST['wp_borgbackup_delete_config'])) {
            $this->handle_config_delete();
        }
        
        // Handle manual backup
        if (isset($_POST['wp_borgbackup_run_backup'])) {
            $this->handle_manual_backup();
        }
    }
    
    /**
     * Handle settings update
     */
    private function handle_settings_update() {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'wp_borgbackup_settings')) {
            wp_die(__('Security check failed', 'wp-borgbackup'));
        }
        
        $tab = sanitize_text_field($_POST['tab']);
        
        switch ($tab) {
            case 'general':
                $settings = array(
                    'enabled' => isset($_POST['enabled']),
                    'debug_mode' => isset($_POST['debug_mode']),
                    'log_retention_days' => intval($_POST['log_retention_days'])
                );
                update_option('wp_borgbackup_settings', $settings);
                break;
                
            case 'borgbase':
                $settings = array(
                    'api_key' => sanitize_text_field($_POST['api_key']),
                    'default_region' => sanitize_text_field($_POST['default_region'])
                );
                update_option('wp_borgbackup_borgbase_settings', $settings);
                break;
                
            case 'backup':
                $settings = array(
                    'include_uploads' => isset($_POST['include_uploads']),
                    'include_themes' => isset($_POST['include_themes']),
                    'include_plugins' => isset($_POST['include_plugins']),
                    'include_database' => isset($_POST['include_database']),
                    'exclude_patterns' => array_filter(array_map('trim', explode("\n", $_POST['exclude_patterns'])))
                );
                update_option('wp_borgbackup_backup_settings', $settings);
                break;
                
            case 'retention':
                $settings = array(
                    'keep_daily' => intval($_POST['keep_daily']),
                    'keep_weekly' => intval($_POST['keep_weekly']),
                    'keep_monthly' => intval($_POST['keep_monthly']),
                    'keep_yearly' => intval($_POST['keep_yearly'])
                );
                update_option('wp_borgbackup_retention_settings', $settings);
                break;
                
            case 'schedule':
                $settings = array(
                    'enabled' => isset($_POST['enabled']),
                    'frequency' => sanitize_text_field($_POST['frequency']),
                    'time' => sanitize_text_field($_POST['time'])
                );
                update_option('wp_borgbackup_schedule_settings', $settings);
                
                // Update scheduled backup
                if ($settings['enabled']) {
                    $this->scheduler->schedule_backup($settings);
                } else {
                    $this->scheduler->unschedule_backup();
                }
                break;
                
            case 'notifications':
                $settings = array(
                    'enabled' => isset($_POST['enabled']),
                    'email' => sanitize_email($_POST['email']),
                    'on_success' => isset($_POST['on_success']),
                    'on_failure' => isset($_POST['on_failure'])
                );
                update_option('wp_borgbackup_notification_settings', $settings);
                break;
        }
        
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully.', 'wp-borgbackup') . '</p></div>';
        });
    }
    
    /**
     * Handle backup configuration save
     */
    private function handle_config_save() {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'wp_borgbackup_config')) {
            wp_die(__('Security check failed', 'wp-borgbackup'));
        }
        
        $config_data = array(
            'name' => sanitize_text_field($_POST['name']),
            'description' => sanitize_textarea_field($_POST['description']),
            'repository_url' => sanitize_text_field($_POST['repository_url']),
            'encryption_passphrase' => sanitize_text_field($_POST['encryption_passphrase']),
            'source_directories' => array_filter(array_map('trim', explode("\n", $_POST['source_directories']))),
            'exclude_patterns' => array_filter(array_map('trim', explode("\n", $_POST['exclude_patterns']))),
            'retention_policy' => array(
                'keep_daily' => intval($_POST['keep_daily']),
                'keep_weekly' => intval($_POST['keep_weekly']),
                'keep_monthly' => intval($_POST['keep_monthly']),
                'keep_yearly' => intval($_POST['keep_yearly'])
            ),
            'status' => sanitize_text_field($_POST['status'])
        );
        
        $config_id = isset($_POST['config_id']) ? intval($_POST['config_id']) : 0;
        
        if ($config_id) {
            // Update existing configuration
            $result = $this->database->update_backup_config($config_id, $config_data);
            $message = __('Backup configuration updated successfully.', 'wp-borgbackup');
        } else {
            // Create new configuration
            $result = $this->database->insert_backup_config($config_data);
            $message = __('Backup configuration created successfully.', 'wp-borgbackup');
        }
        
        if (is_wp_error($result)) {
            add_action('admin_notices', function() use ($result) {
                echo '<div class="notice notice-error is-dismissible"><p>' . $result->get_error_message() . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() use ($message) {
                echo '<div class="notice notice-success is-dismissible"><p>' . $message . '</p></div>';
            });
        }
    }
    
    /**
     * Handle backup configuration deletion
     */
    private function handle_config_delete() {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'wp_borgbackup_delete_config')) {
            wp_die(__('Security check failed', 'wp-borgbackup'));
        }
        
        $config_id = intval($_POST['config_id']);
        
        // Delete associated files
        $borgmatic = new WP_BorgBackup_Borgmatic();
        $borgmatic->delete_config($config_id);
        $borgmatic->delete_ssh_key($config_id);
        
        // Delete from database
        $result = $this->database->delete_backup_config($config_id);
        
        if (is_wp_error($result)) {
            add_action('admin_notices', function() use ($result) {
                echo '<div class="notice notice-error is-dismissible"><p>' . $result->get_error_message() . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success is-dismissible"><p>' . __('Backup configuration deleted successfully.', 'wp-borgbackup') . '</p></div>';
            });
        }
    }
    
    /**
     * Handle manual backup
     */
    private function handle_manual_backup() {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'wp_borgbackup_manual_backup')) {
            wp_die(__('Security check failed', 'wp-borgbackup'));
        }
        
        $config_id = isset($_POST['config_id']) ? intval($_POST['config_id']) : null;
        
        // Run backup in background
        wp_schedule_single_event(time(), 'wp_borgbackup_manual_backup', array($config_id));
        
        add_action('admin_notices', function() {
            echo '<div class="notice notice-info is-dismissible"><p>' . __('Manual backup started. Check the history page for progress.', 'wp-borgbackup') . '</p></div>';
        });
    }
    
    /**
     * Get admin page URL
     */
    public static function get_admin_url($page = 'wp-borgbackup', $args = array()) {
        $url = admin_url('admin.php?page=' . $page);
        
        if (!empty($args)) {
            $url = add_query_arg($args, $url);
        }
        
        return $url;
    }
    
    /**
     * Render settings tabs
     */
    public function render_settings_tabs($active_tab) {
        $tabs = array(
            'general' => __('General', 'wp-borgbackup'),
            'borgbase' => __('BorgBase', 'wp-borgbackup'),
            'backup' => __('Backup', 'wp-borgbackup'),
            'retention' => __('Retention', 'wp-borgbackup'),
            'schedule' => __('Schedule', 'wp-borgbackup'),
            'notifications' => __('Notifications', 'wp-borgbackup')
        );
        
        echo '<h2 class="nav-tab-wrapper">';
        foreach ($tabs as $tab_key => $tab_name) {
            $active_class = $active_tab === $tab_key ? ' nav-tab-active' : '';
            $url = self::get_admin_url('wp-borgbackup-settings', array('tab' => $tab_key));
            echo '<a href="' . esc_url($url) . '" class="nav-tab' . $active_class . '">' . esc_html($tab_name) . '</a>';
        }
        echo '</h2>';
    }
}
