<?php
/**
 * Database management class for WP BorgBackup
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_BorgBackup_Database {
    
    /**
     * Database version
     */
    const DB_VERSION = '1.0.0';
    
    /**
     * Table names
     */
    private $backup_configs_table;
    private $backup_history_table;
    private $backup_logs_table;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        
        $this->backup_configs_table = $wpdb->prefix . 'borgbackup_configs';
        $this->backup_history_table = $wpdb->prefix . 'borgbackup_history';
        $this->backup_logs_table = $wpdb->prefix . 'borgbackup_logs';
    }
    
    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Backup configurations table
        $sql_configs = "CREATE TABLE {$this->backup_configs_table} (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            repository_url varchar(500) NOT NULL,
            ssh_key_path varchar(500),
            encryption_passphrase varchar(255),
            source_directories longtext,
            exclude_patterns longtext,
            retention_policy longtext,
            schedule_settings longtext,
            borgmatic_config longtext,
            status enum('active', 'inactive', 'error') DEFAULT 'inactive',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY name (name)
        ) $charset_collate;";
        
        // Backup history table
        $sql_history = "CREATE TABLE {$this->backup_history_table} (
            id int(11) NOT NULL AUTO_INCREMENT,
            config_id int(11) NOT NULL,
            archive_name varchar(255) NOT NULL,
            backup_type enum('manual', 'scheduled') DEFAULT 'scheduled',
            status enum('running', 'completed', 'failed', 'cancelled') DEFAULT 'running',
            start_time datetime DEFAULT CURRENT_TIMESTAMP,
            end_time datetime NULL,
            duration int(11) NULL,
            files_count int(11) NULL,
            size_original bigint(20) NULL,
            size_compressed bigint(20) NULL,
            size_deduplicated bigint(20) NULL,
            error_message text NULL,
            borgmatic_output longtext NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY config_id (config_id),
            KEY status (status),
            KEY start_time (start_time),
            FOREIGN KEY (config_id) REFERENCES {$this->backup_configs_table}(id) ON DELETE CASCADE
        ) $charset_collate;";
        
        // Backup logs table
        $sql_logs = "CREATE TABLE {$this->backup_logs_table} (
            id int(11) NOT NULL AUTO_INCREMENT,
            backup_id int(11) NULL,
            level enum('debug', 'info', 'warning', 'error', 'critical') DEFAULT 'info',
            message text NOT NULL,
            context longtext NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY backup_id (backup_id),
            KEY level (level),
            KEY created_at (created_at),
            FOREIGN KEY (backup_id) REFERENCES {$this->backup_history_table}(id) ON DELETE SET NULL
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        dbDelta($sql_configs);
        dbDelta($sql_history);
        dbDelta($sql_logs);
        
        // Update database version
        update_option('wp_borgbackup_db_version', self::DB_VERSION);
    }
    
    /**
     * Drop database tables
     */
    public function drop_tables() {
        global $wpdb;
        
        $wpdb->query("DROP TABLE IF EXISTS {$this->backup_logs_table}");
        $wpdb->query("DROP TABLE IF EXISTS {$this->backup_history_table}");
        $wpdb->query("DROP TABLE IF EXISTS {$this->backup_configs_table}");
        
        delete_option('wp_borgbackup_db_version');
    }
    
    /**
     * Check if database needs upgrade
     */
    public function needs_upgrade() {
        $current_version = get_option('wp_borgbackup_db_version', '0.0.0');
        return version_compare($current_version, self::DB_VERSION, '<');
    }
    
    /**
     * Upgrade database if needed
     */
    public function maybe_upgrade() {
        if ($this->needs_upgrade()) {
            $this->create_tables();
        }
    }
    
    /**
     * Insert backup configuration
     */
    public function insert_backup_config($data) {
        global $wpdb;
        
        $defaults = array(
            'name' => '',
            'description' => '',
            'repository_url' => '',
            'ssh_key_path' => '',
            'encryption_passphrase' => '',
            'source_directories' => '',
            'exclude_patterns' => '',
            'retention_policy' => '',
            'schedule_settings' => '',
            'borgmatic_config' => '',
            'status' => 'inactive',
        );
        
        $data = wp_parse_args($data, $defaults);
        
        // Serialize array fields
        $data['source_directories'] = maybe_serialize($data['source_directories']);
        $data['exclude_patterns'] = maybe_serialize($data['exclude_patterns']);
        $data['retention_policy'] = maybe_serialize($data['retention_policy']);
        $data['schedule_settings'] = maybe_serialize($data['schedule_settings']);
        
        $result = $wpdb->insert(
            $this->backup_configs_table,
            $data,
            array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
        );
        
        if ($result === false) {
            return new WP_Error('db_insert_error', __('Failed to insert backup configuration', 'wp-borgbackup'));
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Update backup configuration
     */
    public function update_backup_config($id, $data) {
        global $wpdb;
        
        // Serialize array fields if they exist
        if (isset($data['source_directories'])) {
            $data['source_directories'] = maybe_serialize($data['source_directories']);
        }
        if (isset($data['exclude_patterns'])) {
            $data['exclude_patterns'] = maybe_serialize($data['exclude_patterns']);
        }
        if (isset($data['retention_policy'])) {
            $data['retention_policy'] = maybe_serialize($data['retention_policy']);
        }
        if (isset($data['schedule_settings'])) {
            $data['schedule_settings'] = maybe_serialize($data['schedule_settings']);
        }
        
        $result = $wpdb->update(
            $this->backup_configs_table,
            $data,
            array('id' => $id),
            null,
            array('%d')
        );
        
        if ($result === false) {
            return new WP_Error('db_update_error', __('Failed to update backup configuration', 'wp-borgbackup'));
        }
        
        return $result;
    }
    
    /**
     * Get backup configuration by ID
     */
    public function get_backup_config($id) {
        global $wpdb;
        
        $config = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->backup_configs_table} WHERE id = %d", $id),
            ARRAY_A
        );
        
        if ($config) {
            // Unserialize array fields
            $config['source_directories'] = maybe_unserialize($config['source_directories']);
            $config['exclude_patterns'] = maybe_unserialize($config['exclude_patterns']);
            $config['retention_policy'] = maybe_unserialize($config['retention_policy']);
            $config['schedule_settings'] = maybe_unserialize($config['schedule_settings']);
        }
        
        return $config;
    }
    
    /**
     * Get all backup configurations
     */
    public function get_backup_configs($status = null) {
        global $wpdb;
        
        $sql = "SELECT * FROM {$this->backup_configs_table}";
        $params = array();
        
        if ($status) {
            $sql .= " WHERE status = %s";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        if (!empty($params)) {
            $configs = $wpdb->get_results($wpdb->prepare($sql, $params), ARRAY_A);
        } else {
            $configs = $wpdb->get_results($sql, ARRAY_A);
        }
        
        // Unserialize array fields for each config
        foreach ($configs as &$config) {
            $config['source_directories'] = maybe_unserialize($config['source_directories']);
            $config['exclude_patterns'] = maybe_unserialize($config['exclude_patterns']);
            $config['retention_policy'] = maybe_unserialize($config['retention_policy']);
            $config['schedule_settings'] = maybe_unserialize($config['schedule_settings']);
        }
        
        return $configs;
    }
    
    /**
     * Delete backup configuration
     */
    public function delete_backup_config($id) {
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->backup_configs_table,
            array('id' => $id),
            array('%d')
        );
        
        if ($result === false) {
            return new WP_Error('db_delete_error', __('Failed to delete backup configuration', 'wp-borgbackup'));
        }
        
        return $result;
    }
    
    /**
     * Insert backup history record
     */
    public function insert_backup_history($data) {
        global $wpdb;
        
        $defaults = array(
            'config_id' => 0,
            'archive_name' => '',
            'backup_type' => 'scheduled',
            'status' => 'running',
            'start_time' => current_time('mysql'),
            'end_time' => null,
            'duration' => null,
            'files_count' => null,
            'size_original' => null,
            'size_compressed' => null,
            'size_deduplicated' => null,
            'error_message' => null,
            'borgmatic_output' => null,
        );
        
        $data = wp_parse_args($data, $defaults);
        
        $result = $wpdb->insert(
            $this->backup_history_table,
            $data,
            array('%d', '%s', '%s', '%s', '%s', '%s', '%d', '%d', '%d', '%d', '%d', '%s', '%s')
        );
        
        if ($result === false) {
            return new WP_Error('db_insert_error', __('Failed to insert backup history', 'wp-borgbackup'));
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Update backup history record
     */
    public function update_backup_history($id, $data) {
        global $wpdb;
        
        $result = $wpdb->update(
            $this->backup_history_table,
            $data,
            array('id' => $id),
            null,
            array('%d')
        );
        
        if ($result === false) {
            return new WP_Error('db_update_error', __('Failed to update backup history', 'wp-borgbackup'));
        }
        
        return $result;
    }
    
    /**
     * Get backup history
     */
    public function get_backup_history($config_id = null, $limit = 50, $offset = 0) {
        global $wpdb;
        
        $sql = "SELECT h.*, c.name as config_name 
                FROM {$this->backup_history_table} h 
                LEFT JOIN {$this->backup_configs_table} c ON h.config_id = c.id";
        $params = array();
        
        if ($config_id) {
            $sql .= " WHERE h.config_id = %d";
            $params[] = $config_id;
        }
        
        $sql .= " ORDER BY h.start_time DESC LIMIT %d OFFSET %d";
        $params[] = $limit;
        $params[] = $offset;
        
        return $wpdb->get_results($wpdb->prepare($sql, $params), ARRAY_A);
    }
    
    /**
     * Insert log entry
     */
    public function insert_log($backup_id, $level, $message, $context = null) {
        global $wpdb;
        
        $data = array(
            'backup_id' => $backup_id,
            'level' => $level,
            'message' => $message,
            'context' => maybe_serialize($context),
            'created_at' => current_time('mysql'),
        );
        
        $result = $wpdb->insert(
            $this->backup_logs_table,
            $data,
            array('%d', '%s', '%s', '%s', '%s')
        );
        
        if ($result === false) {
            return new WP_Error('db_insert_error', __('Failed to insert log entry', 'wp-borgbackup'));
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Get logs
     */
    public function get_logs($backup_id = null, $level = null, $limit = 100, $offset = 0) {
        global $wpdb;
        
        $sql = "SELECT * FROM {$this->backup_logs_table}";
        $params = array();
        $where_conditions = array();
        
        if ($backup_id) {
            $where_conditions[] = "backup_id = %d";
            $params[] = $backup_id;
        }
        
        if ($level) {
            $where_conditions[] = "level = %s";
            $params[] = $level;
        }
        
        if (!empty($where_conditions)) {
            $sql .= " WHERE " . implode(' AND ', $where_conditions);
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT %d OFFSET %d";
        $params[] = $limit;
        $params[] = $offset;
        
        $logs = $wpdb->get_results($wpdb->prepare($sql, $params), ARRAY_A);
        
        // Unserialize context for each log
        foreach ($logs as &$log) {
            $log['context'] = maybe_unserialize($log['context']);
        }
        
        return $logs;
    }
    
    /**
     * Clean old logs
     */
    public function clean_old_logs($days = 30) {
        global $wpdb;
        
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        return $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$this->backup_logs_table} WHERE created_at < %s",
                $cutoff_date
            )
        );
    }
}
