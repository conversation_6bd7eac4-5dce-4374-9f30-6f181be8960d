<?php
/**
 * Backup execution engine for WP BorgBackup
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_BorgBackup_Backup_Engine {
    
    /**
     * Database instance
     */
    private $database;
    
    /**
     * Borgmatic instance
     */
    private $borgmatic;
    
    /**
     * Logger instance
     */
    private $logger;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new WP_BorgBackup_Database();
        $this->borgmatic = new WP_BorgBackup_Borgmatic();
        $this->logger = new WP_BorgBackup_Logger();
    }
    
    /**
     * Run backup for all active configurations
     */
    public function run_backup($config_id = null, $backup_type = 'scheduled') {
        $results = array();
        
        if ($config_id) {
            // Run backup for specific configuration
            $config = $this->database->get_backup_config($config_id);
            if ($config) {
                $results[] = $this->run_single_backup($config, $backup_type);
            } else {
                return new WP_Error('config_not_found', __('Backup configuration not found', 'wp-borgbackup'));
            }
        } else {
            // Run backup for all active configurations
            $configs = $this->database->get_backup_configs('active');
            
            foreach ($configs as $config) {
                $results[] = $this->run_single_backup($config, $backup_type);
            }
        }
        
        return $results;
    }
    
    /**
     * Run backup for a single configuration
     */
    private function run_single_backup($config, $backup_type = 'scheduled') {
        $start_time = current_time('mysql');
        $archive_name = $this->generate_archive_name($config);
        
        // Create backup history record
        $backup_id = $this->database->insert_backup_history(array(
            'config_id' => $config['id'],
            'archive_name' => $archive_name,
            'backup_type' => $backup_type,
            'status' => 'running',
            'start_time' => $start_time
        ));
        
        if (is_wp_error($backup_id)) {
            return $backup_id;
        }
        
        $this->logger->log($backup_id, 'info', 'Backup started', array(
            'config_id' => $config['id'],
            'config_name' => $config['name'],
            'backup_type' => $backup_type
        ));
        
        try {
            // Validate Borgmatic installation
            $borgmatic_check = $this->borgmatic->validate_borgmatic_installation();
            if (is_wp_error($borgmatic_check)) {
                throw new Exception($borgmatic_check->get_error_message());
            }
            
            // Generate and save Borgmatic configuration
            $config_file = $this->borgmatic->save_config($config['id'], $config);
            if (is_wp_error($config_file)) {
                throw new Exception($config_file->get_error_message());
            }
            
            $this->logger->log($backup_id, 'info', 'Configuration file generated', array(
                'config_file' => $config_file
            ));
            
            // Save SSH key if provided
            if (!empty($config['ssh_private_key'])) {
                $ssh_key_path = $this->borgmatic->save_ssh_key($config['id'], $config['ssh_private_key']);
                if (is_wp_error($ssh_key_path)) {
                    throw new Exception($ssh_key_path->get_error_message());
                }
                
                // Update config with SSH key path
                $config['ssh_key_path'] = $ssh_key_path;
                $config_file = $this->borgmatic->save_config($config['id'], $config);
            }
            
            // Initialize repository if needed
            $init_result = $this->initialize_repository_if_needed($config, $config_file, $backup_id);
            if (is_wp_error($init_result)) {
                throw new Exception($init_result->get_error_message());
            }
            
            // Run the backup
            $backup_result = $this->execute_borgmatic_backup($config_file, $backup_id);
            if (is_wp_error($backup_result)) {
                throw new Exception($backup_result->get_error_message());
            }
            
            // Parse backup statistics
            $stats = $this->parse_backup_statistics($backup_result['output']);
            
            // Update backup history with success
            $end_time = current_time('mysql');
            $duration = strtotime($end_time) - strtotime($start_time);
            
            $this->database->update_backup_history($backup_id, array(
                'status' => 'completed',
                'end_time' => $end_time,
                'duration' => $duration,
                'files_count' => $stats['files_count'],
                'size_original' => $stats['size_original'],
                'size_compressed' => $stats['size_compressed'],
                'size_deduplicated' => $stats['size_deduplicated'],
                'borgmatic_output' => $backup_result['output']
            ));
            
            $this->logger->log($backup_id, 'info', 'Backup completed successfully', array(
                'duration' => $duration,
                'files_count' => $stats['files_count'],
                'size_original' => $stats['size_original'],
                'size_compressed' => $stats['size_compressed']
            ));
            
            // Run pruning to maintain retention policy
            $this->run_pruning($config_file, $backup_id);
            
            // Send success notification
            $this->send_notification($config, $backup_id, 'success');
            
            return array(
                'success' => true,
                'backup_id' => $backup_id,
                'archive_name' => $archive_name,
                'duration' => $duration,
                'stats' => $stats
            );
            
        } catch (Exception $e) {
            // Update backup history with error
            $end_time = current_time('mysql');
            $duration = strtotime($end_time) - strtotime($start_time);
            
            $this->database->update_backup_history($backup_id, array(
                'status' => 'failed',
                'end_time' => $end_time,
                'duration' => $duration,
                'error_message' => $e->getMessage()
            ));
            
            $this->logger->log($backup_id, 'error', 'Backup failed', array(
                'error' => $e->getMessage(),
                'duration' => $duration
            ));
            
            // Send failure notification
            $this->send_notification($config, $backup_id, 'failure', $e->getMessage());
            
            return new WP_Error('backup_failed', $e->getMessage());
        }
    }
    
    /**
     * Generate archive name
     */
    private function generate_archive_name($config) {
        $site_name = sanitize_title(get_bloginfo('name'));
        $timestamp = date('Y-m-d-H-i-s');
        return $site_name . '-' . $timestamp;
    }
    
    /**
     * Initialize repository if needed
     */
    private function initialize_repository_if_needed($config, $config_file, $backup_id) {
        // Check if repository exists by trying to list archives
        $list_command = $this->borgmatic->get_borgmatic_command($config_file, 'list --short');
        $output = array();
        $return_code = 0;
        
        exec($list_command . ' 2>&1', $output, $return_code);
        
        // If repository doesn't exist or is not initialized, initialize it
        if ($return_code !== 0) {
            $this->logger->log($backup_id, 'info', 'Initializing repository', array(
                'repository_url' => $config['repository_url']
            ));
            
            $init_command = $this->borgmatic->get_borgmatic_command($config_file, 'init --encryption repokey-blake2');
            $output = array();
            $return_code = 0;
            
            exec($init_command . ' 2>&1', $output, $return_code);
            
            if ($return_code !== 0) {
                return new WP_Error('init_failed', 'Failed to initialize repository: ' . implode("\n", $output));
            }
            
            $this->logger->log($backup_id, 'info', 'Repository initialized successfully');
        }
        
        return true;
    }
    
    /**
     * Execute Borgmatic backup
     */
    private function execute_borgmatic_backup($config_file, $backup_id) {
        $command = $this->borgmatic->get_borgmatic_command($config_file, 'create --verbosity 1 --stats');
        
        $this->logger->log($backup_id, 'info', 'Executing backup command', array(
            'command' => $command
        ));
        
        // Set up process execution
        $descriptorspec = array(
            0 => array("pipe", "r"),  // stdin
            1 => array("pipe", "w"),  // stdout
            2 => array("pipe", "w")   // stderr
        );
        
        $process = proc_open($command, $descriptorspec, $pipes);
        
        if (!is_resource($process)) {
            return new WP_Error('process_failed', 'Failed to start backup process');
        }
        
        // Close stdin
        fclose($pipes[0]);
        
        // Read output
        $stdout = stream_get_contents($pipes[1]);
        $stderr = stream_get_contents($pipes[2]);
        
        fclose($pipes[1]);
        fclose($pipes[2]);
        
        $return_code = proc_close($process);
        
        $output = $stdout . $stderr;
        
        if ($return_code !== 0) {
            return new WP_Error('backup_failed', 'Backup command failed: ' . $output);
        }
        
        return array(
            'output' => $output,
            'return_code' => $return_code
        );
    }
    
    /**
     * Parse backup statistics from Borgmatic output
     */
    private function parse_backup_statistics($output) {
        $stats = array(
            'files_count' => null,
            'size_original' => null,
            'size_compressed' => null,
            'size_deduplicated' => null
        );
        
        // Parse Borg statistics from output
        if (preg_match('/Number of files: (\d+)/', $output, $matches)) {
            $stats['files_count'] = (int) $matches[1];
        }
        
        if (preg_match('/Original size: ([\d.]+\s*[KMGT]?B)/', $output, $matches)) {
            $stats['size_original'] = $this->parse_size($matches[1]);
        }
        
        if (preg_match('/Compressed size: ([\d.]+\s*[KMGT]?B)/', $output, $matches)) {
            $stats['size_compressed'] = $this->parse_size($matches[1]);
        }
        
        if (preg_match('/Deduplicated size: ([\d.]+\s*[KMGT]?B)/', $output, $matches)) {
            $stats['size_deduplicated'] = $this->parse_size($matches[1]);
        }
        
        return $stats;
    }
    
    /**
     * Parse size string to bytes
     */
    private function parse_size($size_string) {
        $size_string = trim($size_string);
        $units = array('B' => 1, 'KB' => 1024, 'MB' => 1024*1024, 'GB' => 1024*1024*1024, 'TB' => 1024*1024*1024*1024);
        
        if (preg_match('/^([\d.]+)\s*([KMGT]?B)$/', $size_string, $matches)) {
            $number = (float) $matches[1];
            $unit = $matches[2];
            
            if (isset($units[$unit])) {
                return (int) ($number * $units[$unit]);
            }
        }
        
        return null;
    }
    
    /**
     * Run pruning to maintain retention policy
     */
    private function run_pruning($config_file, $backup_id) {
        $this->logger->log($backup_id, 'info', 'Running pruning to maintain retention policy');
        
        $prune_command = $this->borgmatic->get_borgmatic_command($config_file, 'prune --verbosity 1');
        $output = array();
        $return_code = 0;
        
        exec($prune_command . ' 2>&1', $output, $return_code);
        
        if ($return_code === 0) {
            $this->logger->log($backup_id, 'info', 'Pruning completed successfully');
        } else {
            $this->logger->log($backup_id, 'warning', 'Pruning failed', array(
                'output' => implode("\n", $output)
            ));
        }
    }
    
    /**
     * Send notification
     */
    private function send_notification($config, $backup_id, $status, $error_message = null) {
        $settings = get_option('wp_borgbackup_settings', array());
        
        if (empty($settings['notifications_enabled'])) {
            return;
        }
        
        $admin_email = get_option('admin_email');
        $site_name = get_bloginfo('name');
        $site_url = get_site_url();
        
        if ($status === 'success') {
            $subject = sprintf(__('[%s] Backup Completed Successfully', 'wp-borgbackup'), $site_name);
            $message = sprintf(
                __("Backup for configuration '%s' completed successfully.\n\nSite: %s\nBackup ID: %d\nTime: %s", 'wp-borgbackup'),
                $config['name'],
                $site_url,
                $backup_id,
                current_time('mysql')
            );
        } else {
            $subject = sprintf(__('[%s] Backup Failed', 'wp-borgbackup'), $site_name);
            $message = sprintf(
                __("Backup for configuration '%s' failed.\n\nSite: %s\nBackup ID: %d\nTime: %s\nError: %s", 'wp-borgbackup'),
                $config['name'],
                $site_url,
                $backup_id,
                current_time('mysql'),
                $error_message
            );
        }
        
        wp_mail($admin_email, $subject, $message);
    }
    
    /**
     * Test backup configuration
     */
    public function test_backup_config($config_id) {
        $config = $this->database->get_backup_config($config_id);
        
        if (!$config) {
            return new WP_Error('config_not_found', __('Backup configuration not found', 'wp-borgbackup'));
        }
        
        $results = array();
        
        // Test Borgmatic installation
        $borgmatic_check = $this->borgmatic->validate_borgmatic_installation();
        $results['borgmatic'] = !is_wp_error($borgmatic_check);
        
        // Test BorgBase connection
        if (!empty($config['repository_url'])) {
            $borgbase_api = new WP_BorgBackup_BorgBase_API();
            $connection_test = $borgbase_api->test_connection($config['repository_url']);
            $results['borgbase_connection'] = $connection_test['success'];
        }
        
        // Test SSH key
        if (!empty($config['ssh_key_path']) && file_exists($config['ssh_key_path'])) {
            $results['ssh_key'] = is_readable($config['ssh_key_path']);
        }
        
        // Test source directories
        $results['source_directories'] = true;
        if (!empty($config['source_directories'])) {
            foreach ($config['source_directories'] as $dir) {
                if (!is_dir($dir) || !is_readable($dir)) {
                    $results['source_directories'] = false;
                    break;
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Cancel running backup
     */
    public function cancel_backup($backup_id) {
        $backup = $this->database->get_backup_history(null, 1, 0);
        
        if (empty($backup) || $backup[0]['id'] != $backup_id || $backup[0]['status'] !== 'running') {
            return new WP_Error('backup_not_running', __('Backup is not currently running', 'wp-borgbackup'));
        }
        
        // Update status to cancelled
        $this->database->update_backup_history($backup_id, array(
            'status' => 'cancelled',
            'end_time' => current_time('mysql')
        ));
        
        $this->logger->log($backup_id, 'info', 'Backup cancelled by user');
        
        return true;
    }
}
