<?php
/**
 * Backup scheduler for WP BorgBackup
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_BorgBackup_Scheduler {
    
    /**
     * Hook name for scheduled backups
     */
    const HOOK_NAME = 'wp_borgbackup_scheduled_backup';
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'register_cron_schedules'));
    }
    
    /**
     * Register custom cron schedules
     */
    public function register_cron_schedules() {
        add_filter('cron_schedules', array($this, 'add_cron_schedules'));
    }
    
    /**
     * Add custom cron schedules
     */
    public function add_cron_schedules($schedules) {
        // Add weekly schedule if not exists
        if (!isset($schedules['weekly'])) {
            $schedules['weekly'] = array(
                'interval' => 604800, // 7 days
                'display' => __('Weekly', 'wp-borgbackup')
            );
        }
        
        // Add monthly schedule
        $schedules['monthly'] = array(
            'interval' => 2635200, // 30.5 days
            'display' => __('Monthly', 'wp-borgbackup')
        );
        
        // Add every 6 hours
        $schedules['sixhours'] = array(
            'interval' => 21600, // 6 hours
            'display' => __('Every 6 Hours', 'wp-borgbackup')
        );
        
        // Add every 12 hours
        $schedules['twelvehours'] = array(
            'interval' => 43200, // 12 hours
            'display' => __('Every 12 Hours', 'wp-borgbackup')
        );
        
        return $schedules;
    }
    
    /**
     * Schedule backup
     */
    public function schedule_backup($schedule_settings) {
        // Clear existing scheduled backup
        $this->unschedule_backup();
        
        if (empty($schedule_settings['enabled']) || empty($schedule_settings['frequency'])) {
            return false;
        }
        
        $frequency = $schedule_settings['frequency'];
        $time = isset($schedule_settings['time']) ? $schedule_settings['time'] : '02:00';
        
        // Calculate next run time
        $next_run = $this->calculate_next_run_time($frequency, $time);
        
        // Schedule the event
        $result = wp_schedule_event($next_run, $frequency, self::HOOK_NAME);
        
        if ($result === false) {
            return new WP_Error('schedule_failed', __('Failed to schedule backup', 'wp-borgbackup'));
        }
        
        return $next_run;
    }
    
    /**
     * Unschedule backup
     */
    public function unschedule_backup() {
        $timestamp = wp_next_scheduled(self::HOOK_NAME);
        if ($timestamp) {
            wp_unschedule_event($timestamp, self::HOOK_NAME);
        }
        
        // Clear all scheduled events for this hook
        wp_clear_scheduled_hook(self::HOOK_NAME);
    }
    
    /**
     * Calculate next run time based on frequency and time
     */
    private function calculate_next_run_time($frequency, $time) {
        $current_time = current_time('timestamp');
        
        // Parse time (format: HH:MM)
        $time_parts = explode(':', $time);
        $hour = isset($time_parts[0]) ? (int) $time_parts[0] : 2;
        $minute = isset($time_parts[1]) ? (int) $time_parts[1] : 0;
        
        // Validate hour and minute
        $hour = max(0, min(23, $hour));
        $minute = max(0, min(59, $minute));
        
        // Calculate next run time based on frequency
        switch ($frequency) {
            case 'hourly':
                // Next hour at the specified minute
                $next_run = mktime(date('H', $current_time) + 1, $minute, 0, date('n', $current_time), date('j', $current_time), date('Y', $current_time));
                break;
                
            case 'sixhours':
                // Next 6-hour interval
                $current_hour = date('H', $current_time);
                $next_hour = ceil(($current_hour + 1) / 6) * 6;
                if ($next_hour >= 24) {
                    $next_hour = 0;
                    $current_time = strtotime('+1 day', $current_time);
                }
                $next_run = mktime($next_hour, $minute, 0, date('n', $current_time), date('j', $current_time), date('Y', $current_time));
                break;
                
            case 'twelvehours':
                // Next 12-hour interval
                $current_hour = date('H', $current_time);
                $next_hour = $current_hour < 12 ? 12 : 0;
                if ($next_hour == 0) {
                    $current_time = strtotime('+1 day', $current_time);
                }
                $next_run = mktime($next_hour, $minute, 0, date('n', $current_time), date('j', $current_time), date('Y', $current_time));
                break;
                
            case 'daily':
                // Next day at specified time
                $today_run = mktime($hour, $minute, 0, date('n', $current_time), date('j', $current_time), date('Y', $current_time));
                if ($today_run > $current_time) {
                    $next_run = $today_run;
                } else {
                    $next_run = mktime($hour, $minute, 0, date('n', $current_time), date('j', $current_time) + 1, date('Y', $current_time));
                }
                break;
                
            case 'weekly':
                // Next week at specified time on the same day
                $today_run = mktime($hour, $minute, 0, date('n', $current_time), date('j', $current_time), date('Y', $current_time));
                if ($today_run > $current_time) {
                    $next_run = $today_run;
                } else {
                    $next_run = strtotime('+1 week', $today_run);
                }
                break;
                
            case 'monthly':
                // Next month at specified time on the same day
                $today_run = mktime($hour, $minute, 0, date('n', $current_time), date('j', $current_time), date('Y', $current_time));
                if ($today_run > $current_time) {
                    $next_run = $today_run;
                } else {
                    $next_run = mktime($hour, $minute, 0, date('n', $current_time) + 1, date('j', $current_time), date('Y', $current_time));
                }
                break;
                
            default:
                // Default to daily
                $today_run = mktime($hour, $minute, 0, date('n', $current_time), date('j', $current_time), date('Y', $current_time));
                if ($today_run > $current_time) {
                    $next_run = $today_run;
                } else {
                    $next_run = mktime($hour, $minute, 0, date('n', $current_time), date('j', $current_time) + 1, date('Y', $current_time));
                }
                break;
        }
        
        return $next_run;
    }
    
    /**
     * Get next scheduled backup time
     */
    public function get_next_scheduled_time() {
        return wp_next_scheduled(self::HOOK_NAME);
    }
    
    /**
     * Check if backup is scheduled
     */
    public function is_scheduled() {
        return wp_next_scheduled(self::HOOK_NAME) !== false;
    }
    
    /**
     * Get available frequencies
     */
    public function get_available_frequencies() {
        return array(
            'hourly' => __('Hourly', 'wp-borgbackup'),
            'sixhours' => __('Every 6 Hours', 'wp-borgbackup'),
            'twelvehours' => __('Every 12 Hours', 'wp-borgbackup'),
            'daily' => __('Daily', 'wp-borgbackup'),
            'weekly' => __('Weekly', 'wp-borgbackup'),
            'monthly' => __('Monthly', 'wp-borgbackup')
        );
    }
    
    /**
     * Get schedule status
     */
    public function get_schedule_status() {
        $next_run = $this->get_next_scheduled_time();
        
        if (!$next_run) {
            return array(
                'scheduled' => false,
                'next_run' => null,
                'next_run_formatted' => __('Not scheduled', 'wp-borgbackup'),
                'time_until' => null
            );
        }
        
        $current_time = current_time('timestamp');
        $time_until = $next_run - $current_time;
        
        return array(
            'scheduled' => true,
            'next_run' => $next_run,
            'next_run_formatted' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $next_run),
            'time_until' => $this->format_time_until($time_until)
        );
    }
    
    /**
     * Format time until next backup
     */
    private function format_time_until($seconds) {
        if ($seconds <= 0) {
            return __('Overdue', 'wp-borgbackup');
        }
        
        $units = array(
            'day' => 86400,
            'hour' => 3600,
            'minute' => 60
        );
        
        $parts = array();
        
        foreach ($units as $unit => $value) {
            if ($seconds >= $value) {
                $count = floor($seconds / $value);
                $seconds %= $value;
                
                if ($unit === 'day') {
                    $parts[] = sprintf(_n('%d day', '%d days', $count, 'wp-borgbackup'), $count);
                } elseif ($unit === 'hour') {
                    $parts[] = sprintf(_n('%d hour', '%d hours', $count, 'wp-borgbackup'), $count);
                } elseif ($unit === 'minute') {
                    $parts[] = sprintf(_n('%d minute', '%d minutes', $count, 'wp-borgbackup'), $count);
                }
            }
        }
        
        if (empty($parts)) {
            return __('Less than a minute', 'wp-borgbackup');
        }
        
        return implode(', ', array_slice($parts, 0, 2));
    }
    
    /**
     * Reschedule backup with new settings
     */
    public function reschedule_backup($schedule_settings) {
        return $this->schedule_backup($schedule_settings);
    }
    
    /**
     * Run manual backup (bypass schedule)
     */
    public function run_manual_backup($config_id = null) {
        $backup_engine = new WP_BorgBackup_Backup_Engine();
        return $backup_engine->run_backup($config_id, 'manual');
    }
    
    /**
     * Get cron info for debugging
     */
    public function get_cron_info() {
        $cron_array = _get_cron_array();
        $schedules = wp_get_schedules();
        
        $info = array(
            'wp_cron_disabled' => defined('DISABLE_WP_CRON') && DISABLE_WP_CRON,
            'current_time' => current_time('timestamp'),
            'next_scheduled' => $this->get_next_scheduled_time(),
            'all_scheduled_events' => array()
        );
        
        // Find all events for our hook
        foreach ($cron_array as $timestamp => $cron) {
            if (isset($cron[self::HOOK_NAME])) {
                foreach ($cron[self::HOOK_NAME] as $key => $event) {
                    $info['all_scheduled_events'][] = array(
                        'timestamp' => $timestamp,
                        'formatted_time' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $timestamp),
                        'schedule' => $event['schedule'],
                        'args' => $event['args']
                    );
                }
            }
        }
        
        return $info;
    }
}
