<?php
/**
 * Logger class for WP BorgBackup
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_BorgBackup_Logger {
    
    /**
     * Database instance
     */
    private $database;
    
    /**
     * Log levels
     */
    const LEVEL_DEBUG = 'debug';
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new WP_BorgBackup_Database();
    }
    
    /**
     * Log a message
     */
    public function log($backup_id, $level, $message, $context = null) {
        // Validate log level
        $valid_levels = array(
            self::LEVEL_DEBUG,
            self::LEVEL_INFO,
            self::LEVEL_WARNING,
            self::LEVEL_ERROR,
            self::LEVEL_CRITICAL
        );
        
        if (!in_array($level, $valid_levels)) {
            $level = self::LEVEL_INFO;
        }
        
        // Insert log entry into database
        $result = $this->database->insert_log($backup_id, $level, $message, $context);
        
        // Also log to WordPress debug log if enabled
        if (defined('WP_DEBUG') && WP_DEBUG && defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
            $log_message = sprintf(
                '[WP BorgBackup] [%s] [Backup ID: %s] %s',
                strtoupper($level),
                $backup_id ?: 'N/A',
                $message
            );
            
            if ($context) {
                $log_message .= ' Context: ' . wp_json_encode($context);
            }
            
            error_log($log_message);
        }
        
        return $result;
    }
    
    /**
     * Log debug message
     */
    public function debug($backup_id, $message, $context = null) {
        return $this->log($backup_id, self::LEVEL_DEBUG, $message, $context);
    }
    
    /**
     * Log info message
     */
    public function info($backup_id, $message, $context = null) {
        return $this->log($backup_id, self::LEVEL_INFO, $message, $context);
    }
    
    /**
     * Log warning message
     */
    public function warning($backup_id, $message, $context = null) {
        return $this->log($backup_id, self::LEVEL_WARNING, $message, $context);
    }
    
    /**
     * Log error message
     */
    public function error($backup_id, $message, $context = null) {
        return $this->log($backup_id, self::LEVEL_ERROR, $message, $context);
    }
    
    /**
     * Log critical message
     */
    public function critical($backup_id, $message, $context = null) {
        return $this->log($backup_id, self::LEVEL_CRITICAL, $message, $context);
    }
    
    /**
     * Get logs
     */
    public function get_logs($backup_id = null, $level = null, $limit = 100, $offset = 0) {
        return $this->database->get_logs($backup_id, $level, $limit, $offset);
    }
    
    /**
     * Clean old logs
     */
    public function clean_old_logs($days = 30) {
        return $this->database->clean_old_logs($days);
    }
    
    /**
     * Get log level color for display
     */
    public static function get_level_color($level) {
        $colors = array(
            self::LEVEL_DEBUG => '#6c757d',
            self::LEVEL_INFO => '#17a2b8',
            self::LEVEL_WARNING => '#ffc107',
            self::LEVEL_ERROR => '#dc3545',
            self::LEVEL_CRITICAL => '#721c24'
        );
        
        return isset($colors[$level]) ? $colors[$level] : '#6c757d';
    }
    
    /**
     * Get log level icon for display
     */
    public static function get_level_icon($level) {
        $icons = array(
            self::LEVEL_DEBUG => 'dashicons-info',
            self::LEVEL_INFO => 'dashicons-info-outline',
            self::LEVEL_WARNING => 'dashicons-warning',
            self::LEVEL_ERROR => 'dashicons-dismiss',
            self::LEVEL_CRITICAL => 'dashicons-no-alt'
        );
        
        return isset($icons[$level]) ? $icons[$level] : 'dashicons-info';
    }
}
