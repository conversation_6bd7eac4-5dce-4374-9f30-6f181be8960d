<?php
/**
 * BorgBase API client for WP BorgBackup
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_BorgBackup_BorgBase_API {
    
    /**
     * BorgBase API endpoint
     */
    const API_ENDPOINT = 'https://api.borgbase.com/graphql';
    
    /**
     * API key
     */
    private $api_key;
    
    /**
     * HTTP timeout
     */
    private $timeout = 30;
    
    /**
     * Constructor
     */
    public function __construct($api_key = null) {
        $this->api_key = $api_key ?: $this->get_stored_api_key();
    }
    
    /**
     * Get stored API key from WordPress options
     */
    private function get_stored_api_key() {
        $settings = get_option('wp_borgbackup_borgbase_settings', array());
        return isset($settings['api_key']) ? $settings['api_key'] : '';
    }
    
    /**
     * Set API key
     */
    public function set_api_key($api_key) {
        $this->api_key = $api_key;
    }
    
    /**
     * Test API connection
     */
    public function test_connection($repository_url = null) {
        try {
            $query = '
                query {
                    repoList {
                        id
                        name
                        repoPath
                    }
                }
            ';
            
            $response = $this->make_request($query);
            
            if (is_wp_error($response)) {
                return array(
                    'success' => false,
                    'message' => $response->get_error_message()
                );
            }
            
            if (isset($response['errors'])) {
                return array(
                    'success' => false,
                    'message' => $response['errors'][0]['message']
                );
            }
            
            // If repository URL is provided, check if it exists
            if ($repository_url && isset($response['data']['repoList'])) {
                $repo_found = false;
                foreach ($response['data']['repoList'] as $repo) {
                    if (strpos($repository_url, $repo['repoPath']) !== false) {
                        $repo_found = true;
                        break;
                    }
                }
                
                if (!$repo_found) {
                    return array(
                        'success' => false,
                        'message' => __('Repository not found in your BorgBase account', 'wp-borgbackup')
                    );
                }
            }
            
            return array(
                'success' => true,
                'message' => __('Connection successful', 'wp-borgbackup'),
                'repositories' => $response['data']['repoList']
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $e->getMessage()
            );
        }
    }
    
    /**
     * Get list of repositories
     */
    public function get_repositories() {
        $query = '
            query {
                repoList {
                    id
                    name
                    repoPath
                    region
                    quota
                    quotaEnabled
                    currentUsage
                    lastModified
                    alertDays
                    borgVersion
                    appendOnlyKeys {
                        id
                        name
                        keyType
                    }
                }
            }
        ';
        
        $response = $this->make_request($query);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        if (isset($response['errors'])) {
            return new WP_Error('api_error', $response['errors'][0]['message']);
        }
        
        return $response['data']['repoList'];
    }
    
    /**
     * Get repository details
     */
    public function get_repository($repo_id) {
        $query = '
            query($repoId: String!) {
                repoList(id: $repoId) {
                    id
                    name
                    repoPath
                    region
                    quota
                    quotaEnabled
                    currentUsage
                    lastModified
                    alertDays
                    borgVersion
                    appendOnlyKeys {
                        id
                        name
                        keyType
                        keyData
                    }
                }
            }
        ';
        
        $variables = array('repoId' => $repo_id);
        $response = $this->make_request($query, $variables);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        if (isset($response['errors'])) {
            return new WP_Error('api_error', $response['errors'][0]['message']);
        }
        
        return isset($response['data']['repoList'][0]) ? $response['data']['repoList'][0] : null;
    }
    
    /**
     * Create new repository
     */
    public function create_repository($name, $region = 'us', $quota_enabled = false, $quota = null, $alert_days = 1) {
        $query = '
            mutation($name: String!, $region: String!, $quotaEnabled: Boolean!, $quota: Int, $alertDays: Int!) {
                repoAdd(
                    name: $name,
                    region: $region,
                    quotaEnabled: $quotaEnabled,
                    quota: $quota,
                    alertDays: $alertDays
                ) {
                    repoAdded {
                        id
                        name
                        repoPath
                        region
                    }
                }
            }
        ';
        
        $variables = array(
            'name' => $name,
            'region' => $region,
            'quotaEnabled' => $quota_enabled,
            'quota' => $quota,
            'alertDays' => $alert_days
        );
        
        $response = $this->make_request($query, $variables);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        if (isset($response['errors'])) {
            return new WP_Error('api_error', $response['errors'][0]['message']);
        }
        
        return $response['data']['repoAdd']['repoAdded'];
    }
    
    /**
     * Get SSH keys
     */
    public function get_ssh_keys() {
        $query = '
            query {
                sshList {
                    id
                    name
                    keyType
                    keyData
                    hashMd5
                }
            }
        ';
        
        $response = $this->make_request($query);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        if (isset($response['errors'])) {
            return new WP_Error('api_error', $response['errors'][0]['message']);
        }
        
        return $response['data']['sshList'];
    }
    
    /**
     * Add SSH key
     */
    public function add_ssh_key($name, $key_data) {
        $query = '
            mutation($name: String!, $keyData: String!) {
                sshAdd(name: $name, keyData: $keyData) {
                    keyAdded {
                        id
                        name
                        keyType
                        hashMd5
                    }
                }
            }
        ';
        
        $variables = array(
            'name' => $name,
            'keyData' => $key_data
        );
        
        $response = $this->make_request($query, $variables);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        if (isset($response['errors'])) {
            return new WP_Error('api_error', $response['errors'][0]['message']);
        }
        
        return $response['data']['sshAdd']['keyAdded'];
    }
    
    /**
     * Get repository statistics
     */
    public function get_repository_stats($repo_id) {
        $query = '
            query($repoId: String!) {
                repoList(id: $repoId) {
                    id
                    currentUsage
                    quota
                    quotaEnabled
                    lastModified
                    alertDays
                }
            }
        ';
        
        $variables = array('repoId' => $repo_id);
        $response = $this->make_request($query, $variables);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        if (isset($response['errors'])) {
            return new WP_Error('api_error', $response['errors'][0]['message']);
        }
        
        return isset($response['data']['repoList'][0]) ? $response['data']['repoList'][0] : null;
    }
    
    /**
     * Make GraphQL request to BorgBase API
     */
    private function make_request($query, $variables = null) {
        if (empty($this->api_key)) {
            return new WP_Error('no_api_key', __('BorgBase API key not configured', 'wp-borgbackup'));
        }
        
        $body = array(
            'query' => $query
        );
        
        if ($variables) {
            $body['variables'] = $variables;
        }
        
        $args = array(
            'method' => 'POST',
            'timeout' => $this->timeout,
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->api_key,
                'User-Agent' => 'WP-BorgBackup/' . WP_BORGBACKUP_VERSION
            ),
            'body' => wp_json_encode($body)
        );
        
        $response = wp_remote_request(self::API_ENDPOINT, $args);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code !== 200) {
            return new WP_Error(
                'api_error',
                sprintf(__('API request failed with status %d: %s', 'wp-borgbackup'), $response_code, $response_body)
            );
        }
        
        $data = json_decode($response_body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('json_error', __('Invalid JSON response from API', 'wp-borgbackup'));
        }
        
        return $data;
    }
    
    /**
     * Validate repository URL format
     */
    public function validate_repository_url($url) {
        // BorgBase repository URLs follow the pattern: <EMAIL>:repo
        $pattern = '/^[a-zA-Z0-9]+@[a-zA-Z0-9-]+\.repo\.borgbase\.com:repo$/';
        
        if (!preg_match($pattern, $url)) {
            return new WP_Error(
                'invalid_url',
                __('Invalid BorgBase repository URL format. Expected format: <EMAIL>:repo', 'wp-borgbackup')
            );
        }
        
        return true;
    }
    
    /**
     * Extract repository ID from URL
     */
    public function extract_repo_id_from_url($url) {
        // Extract the server part which contains the repo ID
        if (preg_match('/^[a-zA-Z0-9]+@([a-zA-Z0-9-]+)\.repo\.borgbase\.com:repo$/', $url, $matches)) {
            return $matches[1];
        }
        
        return null;
    }
    
    /**
     * Get account information
     */
    public function get_account_info() {
        $query = '
            query {
                account {
                    id
                    email
                    plan
                    quotaUsed
                    quotaTotal
                }
            }
        ';
        
        $response = $this->make_request($query);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        if (isset($response['errors'])) {
            return new WP_Error('api_error', $response['errors'][0]['message']);
        }
        
        return $response['data']['account'];
    }
    
    /**
     * Format file size for display
     */
    public static function format_bytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB', 'PB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
