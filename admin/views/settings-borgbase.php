<?php
/**
 * BorgBase settings page for WP BorgBackup
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$active_tab = 'borgbase';
?>

<div class="wrap">
    <h1><?php _e('WP BorgBackup Settings', 'wp-borgbackup'); ?></h1>
    
    <?php $this->render_settings_tabs($active_tab); ?>
    
    <div class="settings-section">
        <form method="post" action="">
            <?php wp_nonce_field('wp_borgbackup_settings'); ?>
            <input type="hidden" name="wp_borgbackup_update_settings" value="1">
            <input type="hidden" name="tab" value="borgbase">
            
            <h3><?php _e('BorgBase Configuration', 'wp-borgbackup'); ?></h3>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="borgbase-api-key"><?php _e('API Key', 'wp-borgbackup'); ?></label>
                    </th>
                    <td>
                        <input type="password" id="borgbase-api-key" name="api_key" 
                               value="<?php echo esc_attr($settings['api_key'] ?? ''); ?>" 
                               class="regular-text" autocomplete="off">
                        <p class="description">
                            <?php printf(
                                __('Your BorgBase API key. You can find this in your <a href="%s" target="_blank">BorgBase account settings</a>.', 'wp-borgbackup'),
                                'https://www.borgbase.com/account'
                            ); ?>
                        </p>
                        
                        <div class="connection-test">
                            <button type="button" id="test-connection-btn" class="button button-secondary">
                                <?php _e('Test Connection', 'wp-borgbackup'); ?>
                            </button>
                            <div class="connection-result"></div>
                        </div>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="default-region"><?php _e('Default Region', 'wp-borgbackup'); ?></label>
                    </th>
                    <td>
                        <select id="default-region" name="default_region">
                            <option value="us" <?php selected($settings['default_region'] ?? 'us', 'us'); ?>>
                                <?php _e('United States', 'wp-borgbackup'); ?>
                            </option>
                            <option value="eu" <?php selected($settings['default_region'] ?? 'us', 'eu'); ?>>
                                <?php _e('Europe', 'wp-borgbackup'); ?>
                            </option>
                        </select>
                        <p class="description">
                            <?php _e('Default region for new repositories.', 'wp-borgbackup'); ?>
                        </p>
                    </td>
                </tr>
            </table>
            
            <?php if (!empty($repositories)): ?>
                <h3><?php _e('Available Repositories', 'wp-borgbackup'); ?></h3>
                
                <div class="repositories-list">
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Name', 'wp-borgbackup'); ?></th>
                                <th><?php _e('Repository Path', 'wp-borgbackup'); ?></th>
                                <th><?php _e('Region', 'wp-borgbackup'); ?></th>
                                <th><?php _e('Usage', 'wp-borgbackup'); ?></th>
                                <th><?php _e('Last Modified', 'wp-borgbackup'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($repositories as $repo): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo esc_html($repo['name']); ?></strong>
                                    </td>
                                    <td>
                                        <code><?php echo esc_html($repo['repoPath']); ?></code>
                                        <div class="row-actions">
                                            <span class="copy">
                                                <a href="#" class="copy-repo-path" data-path="<?php echo esc_attr($repo['repoPath']); ?>">
                                                    <?php _e('Copy Path', 'wp-borgbackup'); ?>
                                                </a>
                                            </span>
                                        </div>
                                    </td>
                                    <td><?php echo esc_html(strtoupper($repo['region'])); ?></td>
                                    <td>
                                        <?php if ($repo['quotaEnabled']): ?>
                                            <?php
                                            $usage_percent = $repo['quota'] > 0 ? ($repo['currentUsage'] / $repo['quota']) * 100 : 0;
                                            $usage_class = $usage_percent > 90 ? 'text-error' : ($usage_percent > 75 ? 'text-warning' : 'text-success');
                                            ?>
                                            <span class="<?php echo $usage_class; ?>">
                                                <?php echo WP_BorgBackup_BorgBase_API::format_bytes($repo['currentUsage']); ?> / 
                                                <?php echo WP_BorgBackup_BorgBase_API::format_bytes($repo['quota']); ?>
                                                (<?php echo number_format($usage_percent, 1); ?>%)
                                            </span>
                                        <?php else: ?>
                                            <?php echo WP_BorgBackup_BorgBase_API::format_bytes($repo['currentUsage']); ?>
                                            <small class="text-muted"><?php _e('(No quota)', 'wp-borgbackup'); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($repo['lastModified']): ?>
                                            <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($repo['lastModified']))); ?>
                                        <?php else: ?>
                                            <span class="text-muted"><?php _e('Never', 'wp-borgbackup'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
            
            <?php submit_button(__('Save Settings', 'wp-borgbackup')); ?>
        </form>
    </div>
    
    <?php if (!empty($settings['api_key'])): ?>
        <div class="settings-section">
            <h3><?php _e('Create New Repository', 'wp-borgbackup'); ?></h3>
            
            <form id="create-repo-form">
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="repo-name"><?php _e('Repository Name', 'wp-borgbackup'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="repo-name" name="repo_name" class="regular-text" required>
                            <p class="description">
                                <?php _e('A descriptive name for your new repository.', 'wp-borgbackup'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="repo-region"><?php _e('Region', 'wp-borgbackup'); ?></label>
                        </th>
                        <td>
                            <select id="repo-region" name="repo_region">
                                <option value="us"><?php _e('United States', 'wp-borgbackup'); ?></option>
                                <option value="eu"><?php _e('Europe', 'wp-borgbackup'); ?></option>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="repo-quota-enabled"><?php _e('Enable Quota', 'wp-borgbackup'); ?></label>
                        </th>
                        <td>
                            <label>
                                <input type="checkbox" id="repo-quota-enabled" name="quota_enabled">
                                <?php _e('Set a storage quota for this repository', 'wp-borgbackup'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr id="quota-settings" style="display: none;">
                        <th scope="row">
                            <label for="repo-quota"><?php _e('Quota (GB)', 'wp-borgbackup'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="repo-quota" name="quota" min="1" max="1000" class="small-text">
                            <span><?php _e('GB', 'wp-borgbackup'); ?></span>
                        </td>
                    </tr>
                </table>
                
                <p>
                    <button type="submit" class="button button-primary">
                        <?php _e('Create Repository', 'wp-borgbackup'); ?>
                    </button>
                </p>
            </form>
        </div>
    <?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
    // Toggle quota settings
    $('#repo-quota-enabled').on('change', function() {
        if ($(this).is(':checked')) {
            $('#quota-settings').show();
            $('#repo-quota').prop('required', true);
        } else {
            $('#quota-settings').hide();
            $('#repo-quota').prop('required', false);
        }
    });
    
    // Copy repository path
    $('.copy-repo-path').on('click', function(e) {
        e.preventDefault();
        
        var path = $(this).data('path');
        var $temp = $('<input>');
        $('body').append($temp);
        $temp.val(path).select();
        document.execCommand('copy');
        $temp.remove();
        
        $(this).text('<?php _e('Copied!', 'wp-borgbackup'); ?>');
        setTimeout(() => {
            $(this).text('<?php _e('Copy Path', 'wp-borgbackup'); ?>');
        }, 2000);
    });
    
    // Create repository
    $('#create-repo-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submitBtn = $form.find('button[type="submit"]');
        var originalText = $submitBtn.text();
        
        $submitBtn.prop('disabled', true).text('<?php _e('Creating...', 'wp-borgbackup'); ?>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'wp_borgbackup_create_repository',
                repo_name: $('#repo-name').val(),
                repo_region: $('#repo-region').val(),
                quota_enabled: $('#repo-quota-enabled').is(':checked'),
                quota: $('#repo-quota').val(),
                nonce: '<?php echo wp_create_nonce('wp_borgbackup_create_repo'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert('<?php _e('Repository created successfully!', 'wp-borgbackup'); ?>');
                    location.reload();
                } else {
                    alert(response.data.message || '<?php _e('Failed to create repository.', 'wp-borgbackup'); ?>');
                }
            },
            error: function() {
                alert('<?php _e('Failed to create repository.', 'wp-borgbackup'); ?>');
            },
            complete: function() {
                $submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });
});
</script>
