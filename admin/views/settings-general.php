<?php
/**
 * General settings page for WP BorgBackup
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$active_tab = 'general';
?>

<div class="wrap">
    <h1><?php _e('WP BorgBackup Settings', 'wp-borgbackup'); ?></h1>
    
    <?php $this->render_settings_tabs($active_tab); ?>
    
    <div class="settings-section">
        <form method="post" action="">
            <?php wp_nonce_field('wp_borgbackup_settings'); ?>
            <input type="hidden" name="wp_borgbackup_update_settings" value="1">
            <input type="hidden" name="tab" value="general">
            
            <h3><?php _e('General Settings', 'wp-borgbackup'); ?></h3>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="enabled"><?php _e('Enable Backups', 'wp-borgbackup'); ?></label>
                    </th>
                    <td>
                        <label>
                            <input type="checkbox" id="enabled" name="enabled" value="1" <?php checked(!empty($settings['enabled'])); ?>>
                            <?php _e('Enable automatic backups', 'wp-borgbackup'); ?>
                        </label>
                        <p class="description">
                            <?php _e('When enabled, backups will run according to your schedule settings.', 'wp-borgbackup'); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="debug-mode"><?php _e('Debug Mode', 'wp-borgbackup'); ?></label>
                    </th>
                    <td>
                        <label>
                            <input type="checkbox" id="debug-mode" name="debug_mode" value="1" <?php checked(!empty($settings['debug_mode'])); ?>>
                            <?php _e('Enable debug logging', 'wp-borgbackup'); ?>
                        </label>
                        <p class="description">
                            <?php _e('When enabled, detailed debug information will be logged. Only enable this for troubleshooting.', 'wp-borgbackup'); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="log-retention-days"><?php _e('Log Retention', 'wp-borgbackup'); ?></label>
                    </th>
                    <td>
                        <input type="number" id="log-retention-days" name="log_retention_days" 
                               value="<?php echo esc_attr($settings['log_retention_days'] ?? 30); ?>" 
                               min="1" max="365" class="small-text">
                        <span><?php _e('days', 'wp-borgbackup'); ?></span>
                        <p class="description">
                            <?php _e('How long to keep backup logs. Older logs will be automatically deleted.', 'wp-borgbackup'); ?>
                        </p>
                    </td>
                </tr>
            </table>
            
            <h3><?php _e('System Information', 'wp-borgbackup'); ?></h3>
            
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('WordPress Version', 'wp-borgbackup'); ?></th>
                    <td><code><?php echo get_bloginfo('version'); ?></code></td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('PHP Version', 'wp-borgbackup'); ?></th>
                    <td><code><?php echo PHP_VERSION; ?></code></td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Plugin Version', 'wp-borgbackup'); ?></th>
                    <td><code><?php echo WP_BORGBACKUP_VERSION; ?></code></td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('WordPress Cron', 'wp-borgbackup'); ?></th>
                    <td>
                        <?php if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON): ?>
                            <span class="text-error"><?php _e('Disabled', 'wp-borgbackup'); ?></span>
                            <p class="description">
                                <?php _e('WordPress cron is disabled. Scheduled backups may not work properly.', 'wp-borgbackup'); ?>
                            </p>
                        <?php else: ?>
                            <span class="text-success"><?php _e('Enabled', 'wp-borgbackup'); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Borgmatic Installation', 'wp-borgbackup'); ?></th>
                    <td>
                        <?php
                        $borgmatic = new WP_BorgBackup_Borgmatic();
                        $borgmatic_check = $borgmatic->validate_borgmatic_installation();
                        ?>
                        <?php if (is_wp_error($borgmatic_check)): ?>
                            <span class="text-error"><?php _e('Not Found', 'wp-borgbackup'); ?></span>
                            <p class="description">
                                <?php echo esc_html($borgmatic_check->get_error_message()); ?>
                            </p>
                        <?php else: ?>
                            <span class="text-success"><?php _e('Found', 'wp-borgbackup'); ?></span>
                            <?php if (!empty($borgmatic_check['version'])): ?>
                                <br><code><?php echo esc_html($borgmatic_check['version']); ?></code>
                            <?php endif; ?>
                        <?php endif; ?>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Upload Directory', 'wp-borgbackup'); ?></th>
                    <td>
                        <?php
                        $upload_dir = wp_upload_dir();
                        $borgbackup_dir = $upload_dir['basedir'] . '/wp-borgbackup';
                        ?>
                        <code><?php echo esc_html($borgbackup_dir); ?></code>
                        <?php if (is_writable($borgbackup_dir)): ?>
                            <span class="text-success"><?php _e('(Writable)', 'wp-borgbackup'); ?></span>
                        <?php else: ?>
                            <span class="text-error"><?php _e('(Not Writable)', 'wp-borgbackup'); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
            </table>
            
            <?php submit_button(__('Save Settings', 'wp-borgbackup')); ?>
        </form>
    </div>
    
    <div class="settings-section">
        <h3><?php _e('Maintenance', 'wp-borgbackup'); ?></h3>
        
        <table class="form-table">
            <tr>
                <th scope="row"><?php _e('Clean Old Logs', 'wp-borgbackup'); ?></th>
                <td>
                    <button type="button" class="button button-secondary" id="clean-logs-btn">
                        <?php _e('Clean Old Logs', 'wp-borgbackup'); ?>
                    </button>
                    <p class="description">
                        <?php _e('Remove log entries older than the retention period.', 'wp-borgbackup'); ?>
                    </p>
                </td>
            </tr>
            
            <tr>
                <th scope="row"><?php _e('Reset Plugin', 'wp-borgbackup'); ?></th>
                <td>
                    <button type="button" class="button button-secondary" id="reset-plugin-btn" style="color: #a00;">
                        <?php _e('Reset All Settings', 'wp-borgbackup'); ?>
                    </button>
                    <p class="description">
                        <?php _e('Warning: This will delete all configurations, settings, and logs. This action cannot be undone.', 'wp-borgbackup'); ?>
                    </p>
                </td>
            </tr>
        </table>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    $('#clean-logs-btn').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.text();
        
        if (!confirm('<?php _e('Are you sure you want to clean old logs?', 'wp-borgbackup'); ?>')) {
            return;
        }
        
        $btn.prop('disabled', true).text('<?php _e('Cleaning...', 'wp-borgbackup'); ?>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'wp_borgbackup_clean_logs',
                nonce: '<?php echo wp_create_nonce('wp_borgbackup_clean_logs'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert('<?php _e('Old logs cleaned successfully.', 'wp-borgbackup'); ?>');
                } else {
                    alert('<?php _e('Failed to clean logs.', 'wp-borgbackup'); ?>');
                }
            },
            error: function() {
                alert('<?php _e('Failed to clean logs.', 'wp-borgbackup'); ?>');
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });
    
    $('#reset-plugin-btn').on('click', function() {
        var $btn = $(this);
        
        if (!confirm('<?php _e('Are you sure you want to reset all plugin settings? This action cannot be undone.', 'wp-borgbackup'); ?>')) {
            return;
        }
        
        if (!confirm('<?php _e('This will delete ALL backup configurations, settings, and logs. Are you absolutely sure?', 'wp-borgbackup'); ?>')) {
            return;
        }
        
        $btn.prop('disabled', true).text('<?php _e('Resetting...', 'wp-borgbackup'); ?>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'wp_borgbackup_reset_plugin',
                nonce: '<?php echo wp_create_nonce('wp_borgbackup_reset_plugin'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert('<?php _e('Plugin reset successfully. The page will now reload.', 'wp-borgbackup'); ?>');
                    location.reload();
                } else {
                    alert('<?php _e('Failed to reset plugin.', 'wp-borgbackup'); ?>');
                    $btn.prop('disabled', false).text('<?php _e('Reset All Settings', 'wp-borgbackup'); ?>');
                }
            },
            error: function() {
                alert('<?php _e('Failed to reset plugin.', 'wp-borgbackup'); ?>');
                $btn.prop('disabled', false).text('<?php _e('Reset All Settings', 'wp-borgbackup'); ?>');
            }
        });
    });
});
</script>
