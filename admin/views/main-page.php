<?php
/**
 * Main admin page for WP BorgBackup
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1><?php _e('WP BorgBackup', 'wp-borgbackup'); ?></h1>
    
    <?php if (empty($configs)): ?>
        <div class="notice notice-info">
            <p><?php _e('Welcome to WP BorgBackup! To get started, please configure your BorgBase connection and create your first backup configuration.', 'wp-borgbackup'); ?></p>
            <p>
                <a href="<?php echo esc_url(WP_BorgBackup_Admin::get_admin_url('wp-borgbackup-settings', array('tab' => 'borgbase'))); ?>" class="button button-primary">
                    <?php _e('Configure BorgBase', 'wp-borgbackup'); ?>
                </a>
            </p>
        </div>
    <?php endif; ?>
    
    <div class="wp-borgbackup-dashboard">
        <!-- Status Overview -->
        <div class="wp-borgbackup-status-cards">
            <div class="status-card">
                <h3><?php _e('Backup Status', 'wp-borgbackup'); ?></h3>
                <div class="status-content">
                    <?php if ($schedule_status['scheduled']): ?>
                        <span class="status-indicator status-active"></span>
                        <strong><?php _e('Active', 'wp-borgbackup'); ?></strong>
                        <p><?php printf(__('Next backup: %s', 'wp-borgbackup'), $schedule_status['next_run_formatted']); ?></p>
                        <p><?php printf(__('In %s', 'wp-borgbackup'), $schedule_status['time_until']); ?></p>
                    <?php else: ?>
                        <span class="status-indicator status-inactive"></span>
                        <strong><?php _e('Inactive', 'wp-borgbackup'); ?></strong>
                        <p><?php _e('No backups scheduled', 'wp-borgbackup'); ?></p>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="status-card">
                <h3><?php _e('Configurations', 'wp-borgbackup'); ?></h3>
                <div class="status-content">
                    <span class="status-number"><?php echo count($configs); ?></span>
                    <p><?php _e('Backup configurations', 'wp-borgbackup'); ?></p>
                    <a href="#backup-configs" class="button button-secondary"><?php _e('Manage', 'wp-borgbackup'); ?></a>
                </div>
            </div>
            
            <div class="status-card">
                <h3><?php _e('Recent Backups', 'wp-borgbackup'); ?></h3>
                <div class="status-content">
                    <?php if (!empty($recent_backups)): ?>
                        <?php $latest = $recent_backups[0]; ?>
                        <?php if ($latest['status'] === 'completed'): ?>
                            <span class="status-indicator status-success"></span>
                            <strong><?php _e('Last backup successful', 'wp-borgbackup'); ?></strong>
                        <?php elseif ($latest['status'] === 'failed'): ?>
                            <span class="status-indicator status-error"></span>
                            <strong><?php _e('Last backup failed', 'wp-borgbackup'); ?></strong>
                        <?php else: ?>
                            <span class="status-indicator status-running"></span>
                            <strong><?php _e('Backup in progress', 'wp-borgbackup'); ?></strong>
                        <?php endif; ?>
                        <p><?php echo esc_html($latest['start_time']); ?></p>
                    <?php else: ?>
                        <span class="status-indicator status-inactive"></span>
                        <strong><?php _e('No backups yet', 'wp-borgbackup'); ?></strong>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="wp-borgbackup-quick-actions">
            <h2><?php _e('Quick Actions', 'wp-borgbackup'); ?></h2>
            <div class="action-buttons">
                <?php if (!empty($configs)): ?>
                    <form method="post" style="display: inline;">
                        <?php wp_nonce_field('wp_borgbackup_manual_backup'); ?>
                        <input type="hidden" name="wp_borgbackup_run_backup" value="1">
                        <button type="submit" class="button button-primary" id="run-backup-btn">
                            <?php _e('Run Backup Now', 'wp-borgbackup'); ?>
                        </button>
                    </form>
                <?php endif; ?>
                
                <a href="<?php echo esc_url(WP_BorgBackup_Admin::get_admin_url('wp-borgbackup-settings')); ?>" class="button button-secondary">
                    <?php _e('Settings', 'wp-borgbackup'); ?>
                </a>
                
                <a href="<?php echo esc_url(WP_BorgBackup_Admin::get_admin_url('wp-borgbackup-history')); ?>" class="button button-secondary">
                    <?php _e('View History', 'wp-borgbackup'); ?>
                </a>
                
                <a href="<?php echo esc_url(WP_BorgBackup_Admin::get_admin_url('wp-borgbackup-logs')); ?>" class="button button-secondary">
                    <?php _e('View Logs', 'wp-borgbackup'); ?>
                </a>
            </div>
        </div>
        
        <!-- Backup Configurations -->
        <div id="backup-configs" class="wp-borgbackup-configs">
            <h2><?php _e('Backup Configurations', 'wp-borgbackup'); ?></h2>
            
            <?php if (!empty($configs)): ?>
                <div class="configs-table-wrapper">
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Name', 'wp-borgbackup'); ?></th>
                                <th><?php _e('Repository', 'wp-borgbackup'); ?></th>
                                <th><?php _e('Status', 'wp-borgbackup'); ?></th>
                                <th><?php _e('Last Backup', 'wp-borgbackup'); ?></th>
                                <th><?php _e('Actions', 'wp-borgbackup'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($configs as $config): ?>
                                <?php
                                // Get last backup for this config
                                $last_backup = null;
                                foreach ($recent_backups as $backup) {
                                    if ($backup['config_id'] == $config['id']) {
                                        $last_backup = $backup;
                                        break;
                                    }
                                }
                                ?>
                                <tr>
                                    <td>
                                        <strong><?php echo esc_html($config['name']); ?></strong>
                                        <?php if (!empty($config['description'])): ?>
                                            <br><small><?php echo esc_html($config['description']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <code><?php echo esc_html($config['repository_url']); ?></code>
                                    </td>
                                    <td>
                                        <?php if ($config['status'] === 'active'): ?>
                                            <span class="status-badge status-active"><?php _e('Active', 'wp-borgbackup'); ?></span>
                                        <?php elseif ($config['status'] === 'inactive'): ?>
                                            <span class="status-badge status-inactive"><?php _e('Inactive', 'wp-borgbackup'); ?></span>
                                        <?php else: ?>
                                            <span class="status-badge status-error"><?php _e('Error', 'wp-borgbackup'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($last_backup): ?>
                                            <?php if ($last_backup['status'] === 'completed'): ?>
                                                <span class="backup-status backup-success"><?php _e('Success', 'wp-borgbackup'); ?></span>
                                            <?php elseif ($last_backup['status'] === 'failed'): ?>
                                                <span class="backup-status backup-failed"><?php _e('Failed', 'wp-borgbackup'); ?></span>
                                            <?php elseif ($last_backup['status'] === 'running'): ?>
                                                <span class="backup-status backup-running"><?php _e('Running', 'wp-borgbackup'); ?></span>
                                            <?php endif; ?>
                                            <br><small><?php echo esc_html($last_backup['start_time']); ?></small>
                                        <?php else: ?>
                                            <span class="backup-status backup-none"><?php _e('Never', 'wp-borgbackup'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="row-actions">
                                            <span class="edit">
                                                <a href="#" class="edit-config" data-config-id="<?php echo $config['id']; ?>">
                                                    <?php _e('Edit', 'wp-borgbackup'); ?>
                                                </a>
                                            </span>
                                            
                                            <?php if ($config['status'] === 'active'): ?>
                                                <span class="run">
                                                    | <a href="#" class="run-backup" data-config-id="<?php echo $config['id']; ?>">
                                                        <?php _e('Run Backup', 'wp-borgbackup'); ?>
                                                    </a>
                                                </span>
                                            <?php endif; ?>
                                            
                                            <span class="test">
                                                | <a href="#" class="test-config" data-config-id="<?php echo $config['id']; ?>">
                                                    <?php _e('Test', 'wp-borgbackup'); ?>
                                                </a>
                                            </span>
                                            
                                            <span class="delete">
                                                | <a href="#" class="delete-config" data-config-id="<?php echo $config['id']; ?>" style="color: #a00;">
                                                    <?php _e('Delete', 'wp-borgbackup'); ?>
                                                </a>
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="no-configs">
                    <p><?php _e('No backup configurations found.', 'wp-borgbackup'); ?></p>
                </div>
            <?php endif; ?>
            
            <p>
                <a href="#" id="add-config-btn" class="button button-primary">
                    <?php _e('Add New Configuration', 'wp-borgbackup'); ?>
                </a>
            </p>
        </div>
        
        <!-- Recent Backup History -->
        <?php if (!empty($recent_backups)): ?>
            <div class="wp-borgbackup-recent-history">
                <h2><?php _e('Recent Backup History', 'wp-borgbackup'); ?></h2>
                <div class="history-table-wrapper">
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Archive Name', 'wp-borgbackup'); ?></th>
                                <th><?php _e('Configuration', 'wp-borgbackup'); ?></th>
                                <th><?php _e('Type', 'wp-borgbackup'); ?></th>
                                <th><?php _e('Status', 'wp-borgbackup'); ?></th>
                                <th><?php _e('Start Time', 'wp-borgbackup'); ?></th>
                                <th><?php _e('Duration', 'wp-borgbackup'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($recent_backups, 0, 10) as $backup): ?>
                                <tr>
                                    <td><code><?php echo esc_html($backup['archive_name']); ?></code></td>
                                    <td><?php echo esc_html($backup['config_name'] ?: __('Unknown', 'wp-borgbackup')); ?></td>
                                    <td>
                                        <?php if ($backup['backup_type'] === 'manual'): ?>
                                            <span class="backup-type backup-manual"><?php _e('Manual', 'wp-borgbackup'); ?></span>
                                        <?php else: ?>
                                            <span class="backup-type backup-scheduled"><?php _e('Scheduled', 'wp-borgbackup'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($backup['status'] === 'completed'): ?>
                                            <span class="backup-status backup-success"><?php _e('Completed', 'wp-borgbackup'); ?></span>
                                        <?php elseif ($backup['status'] === 'failed'): ?>
                                            <span class="backup-status backup-failed"><?php _e('Failed', 'wp-borgbackup'); ?></span>
                                        <?php elseif ($backup['status'] === 'running'): ?>
                                            <span class="backup-status backup-running"><?php _e('Running', 'wp-borgbackup'); ?></span>
                                        <?php else: ?>
                                            <span class="backup-status backup-cancelled"><?php _e('Cancelled', 'wp-borgbackup'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo esc_html($backup['start_time']); ?></td>
                                    <td>
                                        <?php if ($backup['duration']): ?>
                                            <?php echo esc_html(gmdate('H:i:s', $backup['duration'])); ?>
                                        <?php else: ?>
                                            —
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <p>
                    <a href="<?php echo esc_url(WP_BorgBackup_Admin::get_admin_url('wp-borgbackup-history')); ?>" class="button button-secondary">
                        <?php _e('View All History', 'wp-borgbackup'); ?>
                    </a>
                </p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Configuration Modal -->
<div id="config-modal" class="wp-borgbackup-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modal-title"><?php _e('Add Backup Configuration', 'wp-borgbackup'); ?></h2>
            <button type="button" class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form id="config-form" method="post">
                <?php wp_nonce_field('wp_borgbackup_config'); ?>
                <input type="hidden" name="wp_borgbackup_save_config" value="1">
                <input type="hidden" name="config_id" id="config-id" value="">
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="config-name"><?php _e('Name', 'wp-borgbackup'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="config-name" name="name" class="regular-text" required>
                            <p class="description"><?php _e('A descriptive name for this backup configuration.', 'wp-borgbackup'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="config-description"><?php _e('Description', 'wp-borgbackup'); ?></label>
                        </th>
                        <td>
                            <textarea id="config-description" name="description" class="large-text" rows="3"></textarea>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="config-repository-url"><?php _e('Repository URL', 'wp-borgbackup'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="config-repository-url" name="repository_url" class="large-text" required>
                            <p class="description"><?php _e('BorgBase repository URL (e.g., <EMAIL>:repo)', 'wp-borgbackup'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="config-passphrase"><?php _e('Encryption Passphrase', 'wp-borgbackup'); ?></label>
                        </th>
                        <td>
                            <input type="password" id="config-passphrase" name="encryption_passphrase" class="regular-text">
                            <p class="description"><?php _e('Passphrase for repository encryption. Leave empty if already set.', 'wp-borgbackup'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="config-status"><?php _e('Status', 'wp-borgbackup'); ?></label>
                        </th>
                        <td>
                            <select id="config-status" name="status">
                                <option value="active"><?php _e('Active', 'wp-borgbackup'); ?></option>
                                <option value="inactive"><?php _e('Inactive', 'wp-borgbackup'); ?></option>
                            </select>
                        </td>
                    </tr>
                </table>
                
                <div class="modal-footer">
                    <button type="button" class="button button-secondary modal-close"><?php _e('Cancel', 'wp-borgbackup'); ?></button>
                    <button type="submit" class="button button-primary"><?php _e('Save Configuration', 'wp-borgbackup'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
