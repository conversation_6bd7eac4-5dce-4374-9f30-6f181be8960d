<?php
/**
 * Plugin Name: WP BorgBackup
 * Plugin URI: https://github.com/your-username/wp-borgbackup
 * Description: WordPress plugin for automated backups using BorgBase and Borgmatic with configurable retention policies and scheduling.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: wp-borgbackup
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WP_BORGBACKUP_VERSION', '1.0.0');
define('WP_BORGBACKUP_PLUGIN_FILE', __FILE__);
define('WP_BORGBACKUP_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WP_BORGBACKUP_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WP_BORGBACKUP_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main WP BorgBackup Plugin Class
 */
class WP_BorgBackup {
    
    /**
     * Plugin instance
     * @var WP_BorgBackup
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     * @return WP_BorgBackup
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('WP_BorgBackup', 'uninstall'));
        
        add_action('init', array($this, 'init'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
        
        // AJAX hooks
        add_action('wp_ajax_wp_borgbackup_test_connection', array($this, 'ajax_test_connection'));
        add_action('wp_ajax_wp_borgbackup_run_backup', array($this, 'ajax_run_backup'));
        
        // Cron hooks
        add_action('wp_borgbackup_scheduled_backup', array($this, 'run_scheduled_backup'));
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once WP_BORGBACKUP_PLUGIN_DIR . 'includes/class-wp-borgbackup-database.php';
        require_once WP_BORGBACKUP_PLUGIN_DIR . 'includes/class-wp-borgbackup-borgbase-api.php';
        require_once WP_BORGBACKUP_PLUGIN_DIR . 'includes/class-wp-borgbackup-borgmatic.php';
        require_once WP_BORGBACKUP_PLUGIN_DIR . 'includes/class-wp-borgbackup-backup-engine.php';
        require_once WP_BORGBACKUP_PLUGIN_DIR . 'includes/class-wp-borgbackup-scheduler.php';
        require_once WP_BORGBACKUP_PLUGIN_DIR . 'includes/class-wp-borgbackup-admin.php';
        require_once WP_BORGBACKUP_PLUGIN_DIR . 'includes/class-wp-borgbackup-logger.php';
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $database = new WP_BorgBackup_Database();
        $database->create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Schedule default backup if enabled
        $this->schedule_default_backup();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled backups
        wp_clear_scheduled_hook('wp_borgbackup_scheduled_backup');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove database tables
        $database = new WP_BorgBackup_Database();
        $database->drop_tables();
        
        // Remove options
        delete_option('wp_borgbackup_settings');
        delete_option('wp_borgbackup_borgbase_settings');
        delete_option('wp_borgbackup_backup_settings');
        delete_option('wp_borgbackup_retention_settings');
        delete_option('wp_borgbackup_schedule_settings');
        
        // Clear scheduled backups
        wp_clear_scheduled_hook('wp_borgbackup_scheduled_backup');
        
        // Remove uploaded files
        $upload_dir = wp_upload_dir();
        $borgbackup_dir = $upload_dir['basedir'] . '/wp-borgbackup';
        if (is_dir($borgbackup_dir)) {
            self::remove_directory($borgbackup_dir);
        }
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain('wp-borgbackup', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Initialize components
        $this->logger = new WP_BorgBackup_Logger();
        $this->scheduler = new WP_BorgBackup_Scheduler();
    }
    
    /**
     * Admin initialization
     */
    public function admin_init() {
        // Check if user has required capabilities
        if (!current_user_can('manage_options')) {
            return;
        }
        
        // Initialize admin interface
        $this->admin = new WP_BorgBackup_Admin();
    }
    
    /**
     * Add admin menu
     */
    public function admin_menu() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        add_menu_page(
            __('WP BorgBackup', 'wp-borgbackup'),
            __('BorgBackup', 'wp-borgbackup'),
            'manage_options',
            'wp-borgbackup',
            array($this->admin, 'display_main_page'),
            'dashicons-backup',
            30
        );
        
        add_submenu_page(
            'wp-borgbackup',
            __('Settings', 'wp-borgbackup'),
            __('Settings', 'wp-borgbackup'),
            'manage_options',
            'wp-borgbackup-settings',
            array($this->admin, 'display_settings_page')
        );
        
        add_submenu_page(
            'wp-borgbackup',
            __('Backup History', 'wp-borgbackup'),
            __('History', 'wp-borgbackup'),
            'manage_options',
            'wp-borgbackup-history',
            array($this->admin, 'display_history_page')
        );
        
        add_submenu_page(
            'wp-borgbackup',
            __('Logs', 'wp-borgbackup'),
            __('Logs', 'wp-borgbackup'),
            'manage_options',
            'wp-borgbackup-logs',
            array($this->admin, 'display_logs_page')
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts($hook) {
        if (strpos($hook, 'wp-borgbackup') === false) {
            return;
        }
        
        wp_enqueue_script(
            'wp-borgbackup-admin',
            WP_BORGBACKUP_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            WP_BORGBACKUP_VERSION,
            true
        );
        
        wp_enqueue_style(
            'wp-borgbackup-admin',
            WP_BORGBACKUP_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            WP_BORGBACKUP_VERSION
        );
        
        wp_localize_script('wp-borgbackup-admin', 'wp_borgbackup_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wp_borgbackup_nonce'),
            'strings' => array(
                'testing_connection' => __('Testing connection...', 'wp-borgbackup'),
                'connection_successful' => __('Connection successful!', 'wp-borgbackup'),
                'connection_failed' => __('Connection failed!', 'wp-borgbackup'),
                'backup_starting' => __('Backup starting...', 'wp-borgbackup'),
                'backup_completed' => __('Backup completed!', 'wp-borgbackup'),
                'backup_failed' => __('Backup failed!', 'wp-borgbackup'),
            )
        ));
    }
    
    /**
     * AJAX handler for testing BorgBase connection
     */
    public function ajax_test_connection() {
        check_ajax_referer('wp_borgbackup_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'wp-borgbackup'));
        }
        
        $api_key = sanitize_text_field($_POST['api_key']);
        $repository_url = sanitize_text_field($_POST['repository_url']);
        
        $borgbase_api = new WP_BorgBackup_BorgBase_API($api_key);
        $result = $borgbase_api->test_connection($repository_url);
        
        wp_send_json($result);
    }
    
    /**
     * AJAX handler for running manual backup
     */
    public function ajax_run_backup() {
        check_ajax_referer('wp_borgbackup_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'wp-borgbackup'));
        }
        
        $backup_engine = new WP_BorgBackup_Backup_Engine();
        $result = $backup_engine->run_backup();
        
        wp_send_json($result);
    }
    
    /**
     * Run scheduled backup
     */
    public function run_scheduled_backup() {
        $backup_engine = new WP_BorgBackup_Backup_Engine();
        $backup_engine->run_backup();
    }
    
    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $default_settings = array(
            'enabled' => false,
            'debug_mode' => false,
            'log_retention_days' => 30,
        );
        add_option('wp_borgbackup_settings', $default_settings);
        
        $default_backup_settings = array(
            'include_uploads' => true,
            'include_themes' => true,
            'include_plugins' => true,
            'include_database' => true,
            'exclude_patterns' => array(
                '*.log',
                '*.tmp',
                'cache/*',
                'wp-content/cache/*',
            ),
        );
        add_option('wp_borgbackup_backup_settings', $default_backup_settings);
        
        $default_retention_settings = array(
            'keep_daily' => 7,
            'keep_weekly' => 4,
            'keep_monthly' => 6,
            'keep_yearly' => 1,
        );
        add_option('wp_borgbackup_retention_settings', $default_retention_settings);
        
        $default_schedule_settings = array(
            'frequency' => 'daily',
            'time' => '02:00',
            'enabled' => false,
        );
        add_option('wp_borgbackup_schedule_settings', $default_schedule_settings);
    }
    
    /**
     * Schedule default backup
     */
    private function schedule_default_backup() {
        $schedule_settings = get_option('wp_borgbackup_schedule_settings');
        if ($schedule_settings['enabled']) {
            $this->scheduler->schedule_backup($schedule_settings);
        }
    }
    
    /**
     * Recursively remove directory
     */
    private static function remove_directory($dir) {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), array('.', '..'));
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                self::remove_directory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }
}

// Initialize the plugin
WP_BorgBackup::get_instance();
