# WP BorgBackup Installation Guide

This guide will walk you through installing and configuring the WP BorgBackup plugin for WordPress.

## Prerequisites

Before installing the plugin, ensure you have:

1. **WordPress 5.0+** with admin access
2. **PHP 7.4+** on your server
3. **SSH access** to your server
4. **BorgBase account** (free tier available)

## Step 1: Install Borgmatic

Borgmatic must be installed on your server before using this plugin.

### Option A: Using pip (Recommended)

```bash
# Install pip if not already installed
sudo apt update
sudo apt install python3-pip

# Install borgmatic
sudo pip3 install borgmatic

# Verify installation
borgmatic --version
```

### Option B: Using package manager (Ubuntu/Debian)

```bash
sudo apt update
sudo apt install borgmatic
```

### Option C: Using Homebrew (macOS)

```bash
brew install borgmatic
```

### Verify Installation

```bash
which borgmatic
borgmatic --version
```

## Step 2: Set up BorgBase Account

1. Go to [BorgBase.com](https://www.borgbase.com/)
2. Create a free account
3. Generate an API key:
   - Go to Account Settings
   - Navigate to API section
   - Create a new API key
   - Copy the key (you'll need it later)

## Step 3: Install WordPress Plugin

### Method A: Upload via WordPress Admin

1. Download the plugin files as a ZIP
2. Go to WordPress Admin → Plugins → Add New
3. Click "Upload Plugin"
4. Select the ZIP file and install
5. Activate the plugin

### Method B: Manual Installation

1. Download/clone the plugin files
2. Upload to `/wp-content/plugins/wp-borgbackup/`
3. Go to WordPress Admin → Plugins
4. Activate "WP BorgBackup"

## Step 4: Configure the Plugin

### 4.1 General Settings

1. Go to **BorgBackup → Settings → General**
2. Enable backups
3. Configure log retention (default: 30 days)
4. Verify system information shows Borgmatic as "Found"

### 4.2 BorgBase Configuration

1. Go to **BorgBackup → Settings → BorgBase**
2. Enter your BorgBase API key
3. Click "Test Connection" to verify
4. Choose default region (US or EU)

### 4.3 Create Repository (Optional)

If you don't have a repository yet:

1. In BorgBase settings, scroll to "Create New Repository"
2. Enter a name for your repository
3. Choose region
4. Optionally set a quota
5. Click "Create Repository"

### 4.4 Backup Settings

1. Go to **BorgBackup → Settings → Backup**
2. Choose what to include:
   - ✅ WordPress uploads
   - ✅ Themes
   - ✅ Plugins  
   - ✅ Database
3. Add exclude patterns if needed (e.g., `*.log`, `cache/*`)

### 4.5 Retention Policy

1. Go to **BorgBackup → Settings → Retention**
2. Configure how long to keep backups:
   - Daily: 7 days
   - Weekly: 4 weeks
   - Monthly: 6 months
   - Yearly: 1 year

### 4.6 Schedule Configuration

1. Go to **BorgBackup → Settings → Schedule**
2. Enable scheduled backups
3. Choose frequency (daily recommended)
4. Set time (e.g., 2:00 AM)

### 4.7 Notifications

1. Go to **BorgBackup → Settings → Notifications**
2. Enable email notifications
3. Set email address (defaults to admin email)
4. Choose when to notify (success/failure)

## Step 5: Create Backup Configuration

1. Go to **BorgBackup** main page
2. Click "Add New Configuration"
3. Fill in details:
   - **Name**: Descriptive name (e.g., "Main Site Backup")
   - **Description**: Optional description
   - **Repository URL**: Copy from BorgBase (format: `<EMAIL>:repo`)
   - **Encryption Passphrase**: Strong passphrase for encryption
   - **Status**: Active
4. Click "Save Configuration"

## Step 6: Test Your Setup

### 6.1 Test Configuration

1. In the main BorgBackup page
2. Find your configuration
3. Click "Test" to verify settings

### 6.2 Run Manual Backup

1. Click "Run Backup" for your configuration
2. Monitor progress in backup history
3. Check logs for any issues

## Step 7: Verify Scheduled Backups

1. Check the main page shows "Next backup" time
2. Wait for the scheduled time or check back later
3. Verify backups appear in history

## Troubleshooting

### Common Issues

**Borgmatic not found**
```bash
# Check if borgmatic is in PATH
which borgmatic

# If not found, create symlink
sudo ln -s /usr/local/bin/borgmatic /usr/bin/borgmatic
```

**Permission errors**
```bash
# Ensure WordPress can write to uploads directory
sudo chown -R www-data:www-data /path/to/wordpress/wp-content/uploads/
sudo chmod -R 755 /path/to/wordpress/wp-content/uploads/
```

**Connection failures**
- Verify API key is correct
- Check repository URL format
- Ensure server can reach borgbase.com

**WordPress cron issues**
```php
// Add to wp-config.php if needed
define('DISABLE_WP_CRON', false);
```

### Debug Mode

Enable debug mode for detailed logging:
1. Go to **BorgBackup → Settings → General**
2. Enable "Debug Mode"
3. Check logs for detailed error information

### Test Connection

Use the built-in connection test:
1. Go to **BorgBackup → Settings → BorgBase**
2. Enter API key
3. Click "Test Connection"

## Security Notes

- SSH keys are stored with 600 permissions
- Configuration files are protected from web access
- API keys are stored encrypted in WordPress database
- All user inputs are sanitized and validated

## Next Steps

After successful installation:

1. **Monitor first few backups** to ensure they complete successfully
2. **Test restore process** by downloading a backup from BorgBase
3. **Set up monitoring** to check backup status regularly
4. **Document your setup** including passphrases and repository details

## Support

If you encounter issues:

1. Check the plugin logs in **BorgBackup → Logs**
2. Enable debug mode for more details
3. Verify Borgmatic installation: `borgmatic --version`
4. Test BorgBase connection manually
5. Check WordPress error logs

For additional help, refer to:
- [Borgmatic documentation](https://torsion.org/borgmatic/)
- [BorgBase documentation](https://docs.borgbase.com/)
- Plugin README.md file
