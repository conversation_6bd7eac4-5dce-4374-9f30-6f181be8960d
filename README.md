# WP BorgBackup

A comprehensive WordPress plugin for automated backups using BorgBase and Borgmatic with configurable retention policies and scheduling.

## Features

- **BorgBase Integration**: Connect your WordPress site to BorgBase for secure offsite backups
- **Borgmatic Configuration**: Automatically generates and manages Borgmatic configuration files
- **Flexible Scheduling**: Daily, weekly, monthly backup schedules with custom time settings
- **Retention Policies**: Configurable retention (daily, weekly, monthly, yearly backups)
- **Database Backups**: Automatic WordPress database backup integration
- **File Selection**: Choose which directories to include/exclude from backups
- **Monitoring**: Comprehensive backup history and logging
- **Email Notifications**: Get notified of backup success/failure
- **WordPress Admin Interface**: Easy-to-use admin interface for all settings

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- [Borgmatic](https://torsion.org/borgmatic/) installed on the server
- [BorgBase](https://www.borgbase.com/) account
- SSH access to your server (for initial setup)

## Installation

### 1. Install Borgmatic

First, install Borgmatic on your server. The exact method depends on your server setup:

#### Using pip (recommended):
```bash
sudo pip3 install borgmatic
```

#### Using package manager (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install borgmatic
```

#### Using Homebrew (macOS):
```bash
brew install borgmatic
```

### 2. Install the Plugin

1. Download the plugin files
2. Upload to your WordPress `/wp-content/plugins/` directory
3. Activate the plugin through the WordPress admin interface

### 3. Configure BorgBase

1. Create a [BorgBase](https://www.borgbase.com/) account
2. Generate an API key in your BorgBase account settings
3. Create a repository (or use the plugin to create one)

## Configuration

### 1. BorgBase Setup

1. Go to **BorgBackup > Settings > BorgBase** in your WordPress admin
2. Enter your BorgBase API key
3. Test the connection
4. Select or create a repository

### 2. Backup Configuration

1. Go to **BorgBackup > Settings > Backup**
2. Choose which directories to include:
   - WordPress uploads
   - Themes
   - Plugins
   - Database
3. Set exclude patterns for files you don't want to backup

### 3. Retention Policy

1. Go to **BorgBackup > Settings > Retention**
2. Configure how long to keep backups:
   - Daily backups: 7 days (default)
   - Weekly backups: 4 weeks (default)
   - Monthly backups: 6 months (default)
   - Yearly backups: 1 year (default)

### 4. Schedule Setup

1. Go to **BorgBackup > Settings > Schedule**
2. Enable scheduled backups
3. Choose frequency (hourly, daily, weekly, monthly)
4. Set the time for backups to run

### 5. Create Backup Configuration

1. Go to **BorgBackup** main page
2. Click "Add New Configuration"
3. Fill in the details:
   - Name and description
   - Repository URL from BorgBase
   - Encryption passphrase
4. Save the configuration

## Usage

### Manual Backups

- Go to the main **BorgBackup** page
- Click "Run Backup Now" for immediate backup
- Monitor progress in the backup history

### Scheduled Backups

- Backups run automatically according to your schedule
- Check the main page for next scheduled backup time
- View history and logs for all backups

### Monitoring

- **History**: View all backup attempts with status and details
- **Logs**: Detailed logging for troubleshooting
- **Notifications**: Email alerts for backup success/failure

## File Structure

```
wp-borgbackup/
├── wp-borgbackup.php              # Main plugin file
├── includes/                      # Core classes
│   ├── class-wp-borgbackup-database.php
│   ├── class-wp-borgbackup-borgbase-api.php
│   ├── class-wp-borgbackup-borgmatic.php
│   ├── class-wp-borgbackup-backup-engine.php
│   ├── class-wp-borgbackup-scheduler.php
│   ├── class-wp-borgbackup-admin.php
│   └── class-wp-borgbackup-logger.php
├── admin/                         # Admin interface
│   └── views/                     # Admin page templates
├── assets/                        # CSS and JavaScript
│   ├── css/
│   └── js/
└── README.md
```

## Security

- SSH keys are stored securely with proper file permissions (600)
- Configuration files are protected from web access
- API keys are stored encrypted in WordPress options
- All user inputs are sanitized and validated

## Troubleshooting

### Common Issues

1. **Borgmatic not found**
   - Ensure Borgmatic is installed and in PATH
   - Check the system information in General settings

2. **Permission errors**
   - Ensure WordPress can write to the uploads directory
   - Check SSH key file permissions

3. **Connection failures**
   - Verify BorgBase API key is correct
   - Test connection in BorgBase settings
   - Check repository URL format

4. **Backup failures**
   - Check logs for detailed error messages
   - Verify repository is initialized
   - Ensure sufficient disk space

### Debug Mode

Enable debug mode in General settings for detailed logging:
1. Go to **BorgBackup > Settings > General**
2. Enable "Debug Mode"
3. Check logs for detailed information

### Log Files

- Plugin logs are stored in the database
- WordPress debug logs (if enabled) contain additional information
- Borgmatic output is captured and stored with each backup

## Support

For support and bug reports:
1. Check the logs for error messages
2. Enable debug mode for more details
3. Create an issue with:
   - WordPress version
   - PHP version
   - Plugin version
   - Error messages from logs

## Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### 1.0.0
- Initial release
- BorgBase API integration
- Borgmatic configuration management
- Scheduled backups with WordPress cron
- Configurable retention policies
- Database backup integration
- Admin interface with monitoring
- Email notifications

## Credits

- Built on [Borgmatic](https://torsion.org/borgmatic/) by Dan Helfman
- Uses [BorgBase](https://www.borgbase.com/) for repository hosting
- Inspired by the need for reliable WordPress backups
