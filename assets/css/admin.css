/* WP BorgBackup Admin Styles */

.wp-borgbackup-dashboard {
    margin-top: 20px;
}

/* Status Cards */
.wp-borgbackup-status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.status-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.status-card h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #1d2327;
}

.status-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    vertical-align: middle;
}

.status-indicator.status-active {
    background-color: #00a32a;
}

.status-indicator.status-inactive {
    background-color: #dcdcde;
}

.status-indicator.status-error {
    background-color: #d63638;
}

.status-indicator.status-running {
    background-color: #0073aa;
    animation: pulse 2s infinite;
}

.status-indicator.status-success {
    background-color: #00a32a;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-number {
    font-size: 32px;
    font-weight: bold;
    color: #0073aa;
    line-height: 1;
    margin-bottom: 5px;
}

/* Quick Actions */
.wp-borgbackup-quick-actions {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 30px;
}

.wp-borgbackup-quick-actions h2 {
    margin: 0 0 15px 0;
    font-size: 18px;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.status-active {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-badge.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.status-error {
    background-color: #f8d7da;
    color: #721c24;
}

/* Backup Status */
.backup-status {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.backup-status.backup-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.backup-status.backup-failed {
    background-color: #f8d7da;
    color: #721c24;
}

.backup-status.backup-running {
    background-color: #cff4fc;
    color: #055160;
}

.backup-status.backup-cancelled {
    background-color: #fff3cd;
    color: #664d03;
}

.backup-status.backup-none {
    background-color: #e2e3e5;
    color: #41464b;
}

/* Backup Type */
.backup-type {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.backup-type.backup-manual {
    background-color: #fff3cd;
    color: #664d03;
}

.backup-type.backup-scheduled {
    background-color: #cff4fc;
    color: #055160;
}

/* Tables */
.configs-table-wrapper,
.history-table-wrapper {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 20px;
}

.wp-list-table th {
    background-color: #f6f7f7;
    border-bottom: 1px solid #c3c4c7;
}

/* Modal */
.wp-borgbackup-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 90%;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #c3c4c7;
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #000;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #c3c4c7;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Settings Tabs */
.nav-tab-wrapper {
    margin-bottom: 20px;
}

/* Settings Forms */
.settings-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.settings-section h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
}

/* Connection Test */
.connection-test {
    margin-top: 10px;
}

.connection-test .button {
    margin-right: 10px;
}

.connection-result {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
    display: none;
}

.connection-result.success {
    background-color: #d1e7dd;
    color: #0f5132;
    border: 1px solid #badbcc;
}

.connection-result.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c2c7;
}

/* Progress Indicators */
.backup-progress {
    margin-top: 10px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f1;
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: #0073aa;
    transition: width 0.3s ease;
    border-radius: 10px;
}

/* Log Levels */
.log-level {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.log-level.debug {
    background-color: #e2e3e5;
    color: #41464b;
}

.log-level.info {
    background-color: #cff4fc;
    color: #055160;
}

.log-level.warning {
    background-color: #fff3cd;
    color: #664d03;
}

.log-level.error {
    background-color: #f8d7da;
    color: #721c24;
}

.log-level.critical {
    background-color: #f8d7da;
    color: #721c24;
    font-weight: bold;
}

/* Responsive */
@media (max-width: 768px) {
    .wp-borgbackup-status-cards {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-success {
    color: #00a32a;
}

.text-error {
    color: #d63638;
}

.text-warning {
    color: #dba617;
}

.text-muted {
    color: #646970;
}

.hidden {
    display: none !important;
}

/* No configs message */
.no-configs {
    text-align: center;
    padding: 40px 20px;
    color: #646970;
}

.no-configs p {
    font-size: 16px;
    margin-bottom: 20px;
}
