/**
 * WP BorgBackup Admin JavaScript
 */

(function($) {
    'use strict';
    
    var WPBorgBackup = {
        
        init: function() {
            this.bindEvents();
            this.initModals();
        },
        
        bindEvents: function() {
            // Modal triggers
            $(document).on('click', '#add-config-btn', this.showAddConfigModal);
            $(document).on('click', '.edit-config', this.showEditConfigModal);
            $(document).on('click', '.modal-close', this.hideModal);
            $(document).on('click', '.wp-borgbackup-modal', this.hideModalOnBackdrop);
            
            // Actions
            $(document).on('click', '.test-config', this.testConfig);
            $(document).on('click', '.run-backup', this.runBackup);
            $(document).on('click', '.delete-config', this.deleteConfig);
            $(document).on('click', '#test-connection-btn', this.testConnection);
            $(document).on('click', '#run-backup-btn', this.runManualBackup);
            
            // Form submissions
            $(document).on('submit', '#config-form', this.saveConfig);
            
            // ESC key to close modal
            $(document).on('keyup', this.handleEscKey);
        },
        
        initModals: function() {
            // Initialize modal elements
            this.$modal = $('#config-modal');
            this.$modalTitle = $('#modal-title');
            this.$configForm = $('#config-form');
        },
        
        showAddConfigModal: function(e) {
            e.preventDefault();
            
            WPBorgBackup.$modalTitle.text(wp_borgbackup_ajax.strings.add_configuration || 'Add Backup Configuration');
            WPBorgBackup.$configForm[0].reset();
            WPBorgBackup.$configForm.find('#config-id').val('');
            WPBorgBackup.$modal.show();
        },
        
        showEditConfigModal: function(e) {
            e.preventDefault();
            
            var configId = $(this).data('config-id');
            
            // Load configuration data via AJAX
            $.ajax({
                url: wp_borgbackup_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wp_borgbackup_get_config',
                    config_id: configId,
                    nonce: wp_borgbackup_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        WPBorgBackup.populateConfigForm(response.data);
                        WPBorgBackup.$modalTitle.text(wp_borgbackup_ajax.strings.edit_configuration || 'Edit Backup Configuration');
                        WPBorgBackup.$modal.show();
                    } else {
                        WPBorgBackup.showNotice('error', response.data.message || 'Failed to load configuration');
                    }
                },
                error: function() {
                    WPBorgBackup.showNotice('error', 'Failed to load configuration');
                }
            });
        },
        
        populateConfigForm: function(config) {
            this.$configForm.find('#config-id').val(config.id);
            this.$configForm.find('#config-name').val(config.name);
            this.$configForm.find('#config-description').val(config.description);
            this.$configForm.find('#config-repository-url').val(config.repository_url);
            this.$configForm.find('#config-status').val(config.status);
        },
        
        hideModal: function(e) {
            e.preventDefault();
            WPBorgBackup.$modal.hide();
        },
        
        hideModalOnBackdrop: function(e) {
            if (e.target === this) {
                WPBorgBackup.$modal.hide();
            }
        },
        
        handleEscKey: function(e) {
            if (e.keyCode === 27) { // ESC key
                WPBorgBackup.$modal.hide();
            }
        },
        
        saveConfig: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $submitBtn = $form.find('button[type="submit"]');
            var originalText = $submitBtn.text();
            
            // Show loading state
            $submitBtn.prop('disabled', true).text(wp_borgbackup_ajax.strings.saving || 'Saving...');
            
            // Submit form normally (let WordPress handle it)
            $form.off('submit').submit();
        },
        
        testConfig: function(e) {
            e.preventDefault();
            
            var configId = $(this).data('config-id');
            var $link = $(this);
            var originalText = $link.text();
            
            $link.text(wp_borgbackup_ajax.strings.testing || 'Testing...');
            
            $.ajax({
                url: wp_borgbackup_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wp_borgbackup_test_config',
                    config_id: configId,
                    nonce: wp_borgbackup_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        WPBorgBackup.showNotice('success', 'Configuration test passed');
                    } else {
                        WPBorgBackup.showNotice('error', response.data.message || 'Configuration test failed');
                    }
                },
                error: function() {
                    WPBorgBackup.showNotice('error', 'Failed to test configuration');
                },
                complete: function() {
                    $link.text(originalText);
                }
            });
        },
        
        runBackup: function(e) {
            e.preventDefault();
            
            var configId = $(this).data('config-id');
            var $link = $(this);
            var originalText = $link.text();
            
            if (!confirm(wp_borgbackup_ajax.strings.confirm_backup || 'Are you sure you want to run a backup now?')) {
                return;
            }
            
            $link.text(wp_borgbackup_ajax.strings.backup_starting || 'Starting...');
            
            $.ajax({
                url: wp_borgbackup_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wp_borgbackup_run_backup',
                    config_id: configId,
                    nonce: wp_borgbackup_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        WPBorgBackup.showNotice('success', wp_borgbackup_ajax.strings.backup_started || 'Backup started successfully');
                        // Optionally refresh the page after a delay
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    } else {
                        WPBorgBackup.showNotice('error', response.data.message || 'Failed to start backup');
                    }
                },
                error: function() {
                    WPBorgBackup.showNotice('error', 'Failed to start backup');
                },
                complete: function() {
                    $link.text(originalText);
                }
            });
        },
        
        runManualBackup: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var originalText = $btn.text();
            
            if (!confirm(wp_borgbackup_ajax.strings.confirm_backup || 'Are you sure you want to run a backup now?')) {
                return;
            }
            
            $btn.prop('disabled', true).html('<span class="spinner"></span>' + (wp_borgbackup_ajax.strings.backup_starting || 'Starting...'));
            
            // Submit the form
            $btn.closest('form').submit();
        },
        
        deleteConfig: function(e) {
            e.preventDefault();
            
            var configId = $(this).data('config-id');
            var $row = $(this).closest('tr');
            
            if (!confirm(wp_borgbackup_ajax.strings.confirm_delete || 'Are you sure you want to delete this configuration? This action cannot be undone.')) {
                return;
            }
            
            $.ajax({
                url: wp_borgbackup_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wp_borgbackup_delete_config',
                    config_id: configId,
                    nonce: wp_borgbackup_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $row.fadeOut(function() {
                            $row.remove();
                        });
                        WPBorgBackup.showNotice('success', 'Configuration deleted successfully');
                    } else {
                        WPBorgBackup.showNotice('error', response.data.message || 'Failed to delete configuration');
                    }
                },
                error: function() {
                    WPBorgBackup.showNotice('error', 'Failed to delete configuration');
                }
            });
        },
        
        testConnection: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var $result = $('.connection-result');
            var apiKey = $('#borgbase-api-key').val();
            var repositoryUrl = $('#repository-url').val();
            
            if (!apiKey) {
                WPBorgBackup.showNotice('error', 'Please enter your BorgBase API key');
                return;
            }
            
            $btn.prop('disabled', true).text(wp_borgbackup_ajax.strings.testing_connection || 'Testing...');
            $result.hide();
            
            $.ajax({
                url: wp_borgbackup_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wp_borgbackup_test_connection',
                    api_key: apiKey,
                    repository_url: repositoryUrl,
                    nonce: wp_borgbackup_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $result.removeClass('error').addClass('success')
                               .text(wp_borgbackup_ajax.strings.connection_successful || 'Connection successful!')
                               .show();
                    } else {
                        $result.removeClass('success').addClass('error')
                               .text(response.message || wp_borgbackup_ajax.strings.connection_failed || 'Connection failed!')
                               .show();
                    }
                },
                error: function() {
                    $result.removeClass('success').addClass('error')
                           .text(wp_borgbackup_ajax.strings.connection_failed || 'Connection failed!')
                           .show();
                },
                complete: function() {
                    $btn.prop('disabled', false).text('Test Connection');
                }
            });
        },
        
        showNotice: function(type, message) {
            var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            $('.wrap h1').after($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut();
            }, 5000);
            
            // Handle dismiss button
            $notice.on('click', '.notice-dismiss', function() {
                $notice.fadeOut();
            });
        },
        
        // Utility function to format bytes
        formatBytes: function(bytes, decimals) {
            if (bytes === 0) return '0 Bytes';
            
            var k = 1024;
            var dm = decimals < 0 ? 0 : decimals;
            var sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
            
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        },
        
        // Utility function to format duration
        formatDuration: function(seconds) {
            var hours = Math.floor(seconds / 3600);
            var minutes = Math.floor((seconds % 3600) / 60);
            var secs = seconds % 60;
            
            var parts = [];
            if (hours > 0) parts.push(hours + 'h');
            if (minutes > 0) parts.push(minutes + 'm');
            if (secs > 0 || parts.length === 0) parts.push(secs + 's');
            
            return parts.join(' ');
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        WPBorgBackup.init();
    });
    
    // Make WPBorgBackup available globally
    window.WPBorgBackup = WPBorgBackup;
    
})(jQuery);
