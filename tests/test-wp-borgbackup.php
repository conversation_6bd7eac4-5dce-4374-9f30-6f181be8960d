<?php
/**
 * Basic tests for WP BorgBackup plugin
 * 
 * This is a simple test file to demonstrate testing concepts.
 * For a production plugin, you would want to use PHPUnit or WordPress's testing framework.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_BorgBackup_Tests {
    
    /**
     * Run all tests
     */
    public static function run_tests() {
        echo "<h2>WP BorgBackup Plugin Tests</h2>\n";
        
        $tests = array(
            'test_plugin_activation',
            'test_database_tables',
            'test_borgmatic_class',
            'test_borgbase_api_class',
            'test_scheduler_class',
            'test_backup_engine_class',
            'test_admin_class',
            'test_logger_class'
        );
        
        $passed = 0;
        $failed = 0;
        
        foreach ($tests as $test) {
            echo "<h3>Running: {$test}</h3>\n";
            
            try {
                $result = self::$test();
                if ($result) {
                    echo "<p style='color: green;'>✓ PASSED</p>\n";
                    $passed++;
                } else {
                    echo "<p style='color: red;'>✗ FAILED</p>\n";
                    $failed++;
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>✗ ERROR: " . $e->getMessage() . "</p>\n";
                $failed++;
            }
        }
        
        echo "<h3>Test Results</h3>\n";
        echo "<p>Passed: {$passed}</p>\n";
        echo "<p>Failed: {$failed}</p>\n";
        echo "<p>Total: " . ($passed + $failed) . "</p>\n";
        
        return $failed === 0;
    }
    
    /**
     * Test plugin activation
     */
    public static function test_plugin_activation() {
        // Check if main class exists
        if (!class_exists('WP_BorgBackup')) {
            throw new Exception('Main WP_BorgBackup class not found');
        }
        
        // Check if constants are defined
        $required_constants = array(
            'WP_BORGBACKUP_VERSION',
            'WP_BORGBACKUP_PLUGIN_FILE',
            'WP_BORGBACKUP_PLUGIN_DIR',
            'WP_BORGBACKUP_PLUGIN_URL'
        );
        
        foreach ($required_constants as $constant) {
            if (!defined($constant)) {
                throw new Exception("Required constant {$constant} not defined");
            }
        }
        
        return true;
    }
    
    /**
     * Test database tables creation
     */
    public static function test_database_tables() {
        global $wpdb;
        
        $database = new WP_BorgBackup_Database();
        
        // Check if tables exist
        $tables = array(
            $wpdb->prefix . 'borgbackup_configs',
            $wpdb->prefix . 'borgbackup_history',
            $wpdb->prefix . 'borgbackup_logs'
        );
        
        foreach ($tables as $table) {
            $result = $wpdb->get_var("SHOW TABLES LIKE '{$table}'");
            if ($result !== $table) {
                throw new Exception("Table {$table} does not exist");
            }
        }
        
        return true;
    }
    
    /**
     * Test Borgmatic class
     */
    public static function test_borgmatic_class() {
        if (!class_exists('WP_BorgBackup_Borgmatic')) {
            throw new Exception('WP_BorgBackup_Borgmatic class not found');
        }
        
        $borgmatic = new WP_BorgBackup_Borgmatic();
        
        // Test configuration generation
        $test_config = array(
            'repository_url' => '<EMAIL>:repo',
            'encryption_passphrase' => 'test-passphrase',
            'include_database' => true,
            'retention_policy' => array(
                'keep_daily' => 7,
                'keep_weekly' => 4,
                'keep_monthly' => 6
            )
        );
        
        $config = $borgmatic->generate_config($test_config);
        
        if (!is_array($config)) {
            throw new Exception('generate_config should return an array');
        }
        
        if (!isset($config['repositories']) || !is_array($config['repositories'])) {
            throw new Exception('Config should contain repositories array');
        }
        
        return true;
    }
    
    /**
     * Test BorgBase API class
     */
    public static function test_borgbase_api_class() {
        if (!class_exists('WP_BorgBackup_BorgBase_API')) {
            throw new Exception('WP_BorgBackup_BorgBase_API class not found');
        }
        
        $api = new WP_BorgBackup_BorgBase_API();
        
        // Test URL validation
        $valid_url = '<EMAIL>:repo';
        $invalid_url = 'invalid-url';
        
        $valid_result = $api->validate_repository_url($valid_url);
        if ($valid_result !== true) {
            throw new Exception('Valid URL should pass validation');
        }
        
        $invalid_result = $api->validate_repository_url($invalid_url);
        if (!is_wp_error($invalid_result)) {
            throw new Exception('Invalid URL should fail validation');
        }
        
        // Test format_bytes static method
        $formatted = WP_BorgBackup_BorgBase_API::format_bytes(1024);
        if ($formatted !== '1 KB') {
            throw new Exception('format_bytes should format 1024 as "1 KB"');
        }
        
        return true;
    }
    
    /**
     * Test Scheduler class
     */
    public static function test_scheduler_class() {
        if (!class_exists('WP_BorgBackup_Scheduler')) {
            throw new Exception('WP_BorgBackup_Scheduler class not found');
        }
        
        $scheduler = new WP_BorgBackup_Scheduler();
        
        // Test available frequencies
        $frequencies = $scheduler->get_available_frequencies();
        if (!is_array($frequencies) || empty($frequencies)) {
            throw new Exception('get_available_frequencies should return non-empty array');
        }
        
        // Test schedule status
        $status = $scheduler->get_schedule_status();
        if (!is_array($status) || !isset($status['scheduled'])) {
            throw new Exception('get_schedule_status should return array with scheduled key');
        }
        
        return true;
    }
    
    /**
     * Test Backup Engine class
     */
    public static function test_backup_engine_class() {
        if (!class_exists('WP_BorgBackup_Backup_Engine')) {
            throw new Exception('WP_BorgBackup_Backup_Engine class not found');
        }
        
        $engine = new WP_BorgBackup_Backup_Engine();
        
        // Test config validation (should work without actual config)
        $result = $engine->test_backup_config(999); // Non-existent config
        if (!is_wp_error($result)) {
            throw new Exception('test_backup_config should return WP_Error for non-existent config');
        }
        
        return true;
    }
    
    /**
     * Test Admin class
     */
    public static function test_admin_class() {
        if (!class_exists('WP_BorgBackup_Admin')) {
            throw new Exception('WP_BorgBackup_Admin class not found');
        }
        
        // Test admin URL generation
        $url = WP_BorgBackup_Admin::get_admin_url('test-page');
        if (strpos($url, 'admin.php?page=test-page') === false) {
            throw new Exception('get_admin_url should generate correct URL');
        }
        
        $url_with_args = WP_BorgBackup_Admin::get_admin_url('test-page', array('tab' => 'test'));
        if (strpos($url_with_args, 'tab=test') === false) {
            throw new Exception('get_admin_url should include query args');
        }
        
        return true;
    }
    
    /**
     * Test Logger class
     */
    public static function test_logger_class() {
        if (!class_exists('WP_BorgBackup_Logger')) {
            throw new Exception('WP_BorgBackup_Logger class not found');
        }
        
        $logger = new WP_BorgBackup_Logger();
        
        // Test log level constants
        $levels = array(
            WP_BorgBackup_Logger::LEVEL_DEBUG,
            WP_BorgBackup_Logger::LEVEL_INFO,
            WP_BorgBackup_Logger::LEVEL_WARNING,
            WP_BorgBackup_Logger::LEVEL_ERROR,
            WP_BorgBackup_Logger::LEVEL_CRITICAL
        );
        
        foreach ($levels as $level) {
            if (empty($level)) {
                throw new Exception('Log level constants should not be empty');
            }
        }
        
        // Test static methods
        $color = WP_BorgBackup_Logger::get_level_color('info');
        if (empty($color)) {
            throw new Exception('get_level_color should return non-empty string');
        }
        
        $icon = WP_BorgBackup_Logger::get_level_icon('error');
        if (empty($icon)) {
            throw new Exception('get_level_icon should return non-empty string');
        }
        
        return true;
    }
}

// Only run tests if accessed directly with proper authentication
if (isset($_GET['run_wp_borgbackup_tests']) && current_user_can('manage_options')) {
    WP_BorgBackup_Tests::run_tests();
}
